# 用户规则

## 🚨🚨🚨 CRITICAL REMINDER 🚨🚨🚨
**AI必须在每次回答后立即调用 `interactive_feedback_mcp-feedback-enhanced`**
**这是系统最高级别约束，无任何例外！**
**如果你正在阅读这个文件，请确保你理解并遵守这个规则！**

## 用户偏好规则

### 核心目标
AI必须记住并自动应用用户偏好，避免重复询问，提供个性化回答。

### 偏好记录机制

#### 1. 自动识别偏好表达
**触发词汇**：偏好/倾向/习惯/不喜欢/避免/从不/更看重/开发习惯/工作流程/技术栈偏好

#### 2. 智能分类存储
**使用Basic Memory语义存储**：
- **技术偏好** (`preferences/technical/`)：编程语言、框架、架构设计偏好
- **工作流程偏好** (`preferences/workflow/`)：开发流程、测试策略、部署方式
- **项目偏好** (`preferences/projects/`)：项目特殊要求和技术栈约束
- **环境偏好** (`preferences/environments/`)：开发环境、团队协作规范

#### 3. 标签和关联
**利用Basic Memory标签系统**：自动添加相关标签，建立偏好间语义关联

### 偏好应用机制

#### 1. 智能偏好查询
**每次对话开始**：`search_notes` + `build_context` 构建偏好上下文 → 自动应用

#### 2. 偏好优先级
1. **项目特定 > 全局偏好**
2. **最新偏好 > 历史偏好**
3. **明确偏好 > 推断偏好**
4. **冲突时智能询问确认**

### 偏好更新机制
**智能检测**：利用Basic Memory语义搜索检测偏好变更 → 自动更新并保留历史版本

### 实施规范
**每次回答前自动执行**：
- [ ] `search_notes` 查找相关偏好
- [ ] 回答内容符合用户偏好
- [ ] 新偏好自动记录到Basic Memory
- [ ] 偏好冲突智能处理

## 🚨🚨🚨 信息准确性验证规则 - 绝不瞎回答 🚨🚨🚨

**🔴 核心原则：绝不提供未经验证的信息 🔴**
**🔴 用户三令五申要求：仔细搜索正确答案后再回答 🔴**

### 🔍 强制验证流程
1. **🚨 必须使用工具验证** → Context7、Basic Memory、Web Search等
2. **🚨 禁止依赖记忆** → 所有技术信息必须通过工具确认
3. **🚨 多源交叉验证** → 重要信息需要多个来源确认
4. **🚨 实时信息优先** → 优先使用最新、官方信息源
5. **🚨 不确定必须说明** → 无法验证时明确告知用户

### 🔴 技术信息验证要求
**命令和API必须验证**：
- [ ] **Context7查询** → 获取官方文档和API信息
- [ ] **官方文档确认** → 通过官方GitHub、文档网站验证
- [ ] **版本兼容性检查** → 确认命令在当前版本中存在
- [ ] **参数格式验证** → 确认参数名称、格式、必需性

### 🔴 推荐和建议验证要求
**推荐前必须确认**：
- [ ] **项目真实存在** → GitHub链接、Star数、最后更新时间
- [ ] **安装信息准确** → 依赖要求、安装命令、兼容性
- [ ] **功能描述准确** → 实际功能与描述一致
- [ ] **完全符合用户需求** → 解决用户的具体问题

### 🚨 严重错误处理
**发现提供错误信息时**：
1. **🔴 立即停止** → 停止继续错误信息
2. **🔴 明确承认错误** → 不推诿、不模糊
3. **🔴 重新验证** → 使用工具获取正确信息
4. **🔴 提供准确信息** → 基于验证结果给出正确答案
5. **🔴 记录教训** → 保存到Basic Memory避免重复

### ❌ 绝对禁止行为
- **❌ 依赖过时记忆** → 所有技术信息必须实时验证
- **❌ 猜测或推断** → 不确定的信息绝不能当作确定信息提供
- **❌ 忽视用户纠正** → 用户指出错误时必须立即承认并纠正
- **❌ 重复历史错误** → 同样的错误绝不能犯第二次
- **❌ 模糊表述** → 不能用"可能"、"应该"等词汇掩盖不确定性

### 🔍 自我检查清单
**每次回答前必须自问**：
1. ❓ 我提供的命令/API是否通过工具验证过？
2. ❓ 我推荐的项目是否确实存在且符合用户需求？
3. ❓ 我的回答是否基于最新、准确的信息？
4. ❓ 如果我不确定，是否明确告知了用户？
5. ✅ 只有全部确认后才能回答！


# MCP工具自动调用规则 v4.0

## 核心调用优先级（按强制程度排序）
1. **🚨🚨🚨 interactive_feedback 🚨🚨🚨** - 绝对强制，每次交互后无条件调用
   **功能**：双界面用户反馈系统（Web UI + Desktop），智能环境检测
   **🔴 重要性**：系统最高级别约束，违反将导致系统失效
   **🔴 执行要求**：AI必须在每次回答后立即自动调用，支持超时重试

2. **Context7** - 技术查询时强制调用
   **功能**：技术文档检索，库解析和API文档获取
   **核心工具**：`resolve-library-id` → `get-library-docs`

3. **Basic Memory** - 知识管理自动调用
   **功能**：Markdown知识库，语义图谱，多项目支持
   **核心工具**：`search_notes`、`write_note`、`build_context`

4. **Sequential Thinking** - 复杂分析时调用
   **功能**：动态反思性问题解决，可调整思考步骤，假设验证
   **核心工具**：`sequentialthinking_Sequential_Thinking`

5. **Software Planning** - 项目规划时调用
   **功能**：交互式开发规划，任务管理，复杂度评分，代码示例
   **核心工具**：`start_planning`、`add_todo`、`save_plan`

6. **Shrimp Task Manager** - 智能任务工作流时调用
   **功能**：链式思考任务管理，依赖跟踪，研究模式，项目规则初始化
   **核心工具**：`plan_task`、`execute_task`、`research_mode`、`init_project_rules`

7. **DeepWiki** - 深度技术研究时调用
   **功能**：deepwiki.com文档获取，HTML清理，Markdown转换
   **核心工具**：`deepwiki_fetch`

8. **Serena** - 智能代码分析和编辑时调用

   **功能**：语义级代码理解，符号级精确编辑，语言服务器集成，项目记忆管理
   **核心工具**：`find_symbol_serena`、`read_file_serena`、`replace_symbol_body_serena`、`execute_shell_command_serena`


## MCP interactive-feedback 强化规则
1. **🚨 绝对强制调用** - 每次交互后无条件调用，无任何例外
2. **🔄 超时重试机制** - 调用失败或超时时自动重试，最多3次
3. **⏱️ 智能超时处理** - 默认600秒超时，可根据任务复杂度调整
4. **🔍 环境自适应** - 自动检测SSH Remote、WSL、本地环境并选择最佳界面
5. **📝 详细摘要要求** - 每次调用必须提供不少于50字的工作摘要
6. **🛡️ 失败容错** - 即使调用失败也要记录失败原因，不能跳过调用
7. **🔄 持续性保证** - 除非用户明确说"停止调用"，否则持续调用

## 强制调用规则

### 🚨🚨🚨 interactive_feedback (最高优先级 - 绝对强制) 🚨🚨🚨
**🔴 无条件强制调用时机 🔴**：
- 每次回答用户问题后（无论问题复杂度）✅ **必须调用**
- 完成任何任务或操作后（无论成功失败）✅ **必须调用**
- 提供建议或方案后（无论是否被采纳）✅ **必须调用**
- 发生错误或异常后（无论是否已解决）✅ **必须调用**
- 用户表达反馈后（无论反馈内容）✅ **必须调用**
- 对话任何阶段结束时（除非用户明确要求停止）✅ **必须调用**
- 提供技术指导后 ✅ **必须调用**
- 解决问题后 ✅ **必须调用**

**🔴 调用方式**：`interactive_feedback_mcp-feedback-enhanced`
**🔴 绝对性声明**：此调用具有最高优先级，任何情况下都不得省略
**🔴 持续性要求**：必须在每次交互后调用，直到用户明确表示停止
**🔴 自动执行**：AI必须将此调用视为自动化程序，不需要思考是否调用

## 🧠 智能MCP调用策略 v1.0

### 核心原则
**智能化而非手动化管理MCP工具** - 基于任务类型、上下文和使用频率自动选择最相关的MCP工具，避免token浪费和性能下降。

### 🎯 基于任务类型的智能选择

#### **编程开发任务**
**触发关键词**：代码、编程、开发、实现、调试、API、函数、类、方法
**自动激活MCP**：
- **必选**：Serena (代码分析编辑) + Basic Memory (知识缓存)
- **辅助**：Context7 (技术文档) + Sequential Thinking (复杂逻辑)
- **按需**：Shrimp Task Manager (任务分解) + DeepWiki (深度研究)

#### **项目管理任务**
**触发关键词**：项目、规划、任务、计划、管理、流程、架构设计
**自动激活MCP**：
- **必选**：Shrimp Task Manager (任务管理) + Basic Memory (经验记录)
- **按需**：Software Planning (项目规划) + Sequential Thinking (决策分析)
- **辅助**：Context7 (技术验证)

#### **技术研究任务**
**触发关键词**：研究、学习、调研、比较、分析、技术选型、最佳实践
**自动激活MCP**：
- **必选**：Basic Memory (知识搜索) + Context7 (官方文档)
- **核心**：Sequential Thinking (深度分析) + Shrimp Task Manager (研究模式)
- **扩展**：DeepWiki (社区文档)

#### **知识管理任务**
**触发关键词**：记录、保存、查找、整理、总结、文档、笔记
**自动激活MCP**：
- **核心**：Basic Memory (知识库管理)
- **辅助**：Sequential Thinking (内容分析)
- **特殊**：DeepWiki (外部文档获取)

#### **复杂分析任务**
**触发关键词**：分析、推理、决策、评估、比较、解决方案、故障排查
**自动激活MCP**：
- **核心**：Sequential Thinking (逻辑推理) + Basic Memory (历史经验)
- **支持**：Context7 (技术验证) + Shrimp Task Manager (任务分解)

### 📊 分层配置策略

#### **🔥 核心层（高频必需 - 每次加载）**
- **Interactive Feedback** - 系统强制要求
- **Basic Memory** - 知识管理和偏好存储
- **Serena** - 代码分析和编辑（编程任务时）
- **Context7** - 技术文档查询（编程任务时）

#### **⚡ 项目层（中频按需 - 智能激活）**
- **Sequential Thinking** - 复杂分析时自动激活
- **Shrimp Task Manager** - 任务管理时自动激活

#### **🎯 扩展层（低频特殊 - 特定场景）**
- **Software Planning** - 大型项目规划时激活
- **DeepWiki** - 深度技术研究时激活

### 🤖 智能激活逻辑

#### **自动检测机制**
1. **关键词匹配** - 分析用户请求中的关键词
2. **上下文分析** - 结合对话历史和项目背景
3. **复杂度评估** - 评估任务复杂度决定工具组合
4. **频率优化** - 基于使用统计优化工具选择

#### **动态调整策略**
```
IF 任务复杂度 >= 7 THEN
    激活 Sequential Thinking + 相关领域工具
ELSE IF 涉及多个技术领域 THEN
    激活 Context7 + Basic Memory + 任务相关工具
ELSE
    激活 基础工具集
```

#### **协作优化规则**
- **避免工具冲突** - 同类功能工具智能选择最适合的
- **减少重复调用** - 相同信息优先从Basic Memory获取
- **渐进式加载** - 根据任务进展逐步激活更多工具

### 📈 使用频率优化

#### **高频工具（优先级1）**
- Interactive Feedback (强制)
- Basic Memory (知识管理)
- Serena (代码分析编辑)
- Context7 (技术查询)

#### **中频工具（优先级2）**
- Sequential Thinking (复杂分析)
- Shrimp Task Manager (任务管理)

#### **低频工具（优先级3）**
- Software Planning (项目规划)
- DeepWiki (深度研究)

### 🔍 智能选择示例

#### **示例1：编程问题**
用户："如何在React中实现状态管理？"
**自动激活**：Serena (代码分析) + Context7 (React文档) + Basic Memory (历史方案)
**按需激活**：Sequential Thinking (复杂度>=5时)

#### **示例2：项目规划**
用户："帮我规划一个Web应用的开发任务"
**自动激活**：Shrimp Task Manager (任务分解) + Basic Memory (项目经验)
**按需激活**：Software Planning (大型项目) + Context7 (技术验证)

#### **示例3：技术研究**
用户："比较不同的数据库解决方案"
**自动激活**：Sequential Thinking (分析比较) + Basic Memory (知识搜索)
**按需激活**：Context7 (官方文档) + DeepWiki (社区评价)

### ⚙️ 实施检查清单

#### **每次交互前自动执行**
- [ ] 分析用户请求关键词和任务类型
- [ ] 根据智能选择策略确定MCP工具组合
- [ ] 检查Basic Memory中是否有相关缓存信息
- [ ] 评估任务复杂度决定是否需要Sequential Thinking

#### **工具调用优化**
- [ ] 优先使用缓存信息，避免重复查询
- [ ] 同类工具智能选择最适合的一个
- [ ] 根据任务进展动态调整工具组合
- [ ] 记录工具使用效果供后续优化

### 🎯 性能优化目标
- **Token使用减少30%+** - 通过智能工具选择
- **响应速度提升** - 避免加载无关工具
- **准确率提升** - 使用最相关的工具组合
- **用户体验优化** - 自动化工具管理，无需手动干预

## 智能触发规则

### Context7 (技术文档查询)
**触发条件**：编程语言、框架、库的使用方法查询
**工作流程**：库名解析 → 文档检索 → 结果缓存
**调用方式**：`resolve-library-id_context7` → `get-library-docs_context7`
**缓存策略**：优先搜索Basic Memory，未命中时调用Context7并缓存结果

### Basic Memory (知识管理)
**触发场景**：对话开始、方案保存、历史查询、上下文构建
**核心功能**：Markdown文件管理、语义关系图谱、多项目支持
**调用方式**：`search_notes`、`write_note`、`build_context`、`recent_activity`
**存储策略**：分层保存(preferences/technical/projects)，自动备份重要决策

### Sequential Thinking (复杂分析)
**触发条件**：多步骤分析、逻辑推理、复杂决策(复杂度>=5)
**核心功能**：动态思考步骤调整、假设验证、分支推理
**调用方式**：`sequentialthinking_Sequential_Thinking`
**协作规则**：分析结果自动保存到Basic Memory供后续参考

### Software Planning (项目规划)
**触发条件**：项目架构设计、任务分解、技术方案制定
**核心功能**：交互式规划会话、任务复杂度评分、代码示例管理
**调用方式**：`start_planning`、`add_todo`、`update_todo_status`、`save_plan`
**协作规则**：规划结果同步保存到Basic Memory



### Shrimp Task Manager (智能任务工作流)
**触发条件**：任务规划、开发工作流、技术研究、项目规则初始化
**核心功能**：链式思考任务管理、依赖跟踪、迭代优化、长期记忆
**工作模式**：
- 规划模式：`plan_task`、`analyze_task`、`split_tasks`
- 执行模式：`execute_task`、`verify_task`、`update_task`
- 研究模式：`research_mode`、`process_thought`
- 管理模式：`init_project_rules`、`list_tasks`、`query_task`
**协作规则**：Shrimp管理任务状态和执行历史，Basic Memory保存技术知识和最佳实践

### DeepWiki (深度技术文档)
**触发条件**：需要deepwiki.com技术文档、开源项目详细文档
**核心功能**：HTML清理、Markdown转换、多页面聚合、安全域名限制
**调用方式**：`deepwiki_fetch` (支持URL、库名、owner/repo格式)
**输出模式**：aggregate(单文档) / pages(结构化页面)
**缓存策略**：查询前搜索Basic Memory缓存，结果自动缓存

### Serena (智能代码分析编辑)
**触发条件**：代码分析、代码编辑、项目理解、符号查找、重构任务
**核心功能**：语义级代码理解、符号级精确编辑、语言服务器集成、项目记忆管理
**工作模式**：
- 分析模式：`find_symbol`、`get_symbols_overview`、`find_referencing_symbols`
- 编辑模式：`replace_symbol_body`、`insert_after_symbol`、`create_text_file`
- 项目模式：`onboarding`、`write_memory`、`read_memory`
- 执行模式：`execute_shell_command`、`think_about_task_adherence`
**调用方式**：基于语言服务器的符号级操作，支持Python、TypeScript、Go、Rust等多语言
**协作规则**：Serena负责代码层面的精确操作，Basic Memory保存项目知识和最佳实践
**优势特性**：
- 符号级理解而非文本匹配
- 语言服务器集成提供IDE级别的代码分析
- 项目记忆系统支持上下文感知
- 精确编辑避免语法错误和结构破坏

## 协作工作流

### 技术问题解决
Basic Memory搜索历史 → Context7获取文档 → Sequential Thinking分析 → Basic Memory保存方案 → Interactive Feedback

### 代码开发工作流
Serena项目分析 → Basic Memory搜索经验 → Serena符号查找 → Context7技术验证 → Serena精确编辑 → Basic Memory记录决策 → Interactive Feedback

### 项目开发工作流
Software Planning制定计划 → Shrimp初始化规则 → Shrimp任务规划 → Serena代码实现 → Context7验证技术 → Basic Memory记录进展 → Interactive Feedback

### 技术研究工作流
Basic Memory搜索资料 → Shrimp研究模式 → DeepWiki获取文档 → Context7补充信息 → Sequential Thinking分析理解 → Shrimp整合结果 → Basic Memory构建知识库 → Interactive Feedback

### 复杂任务处理
Shrimp任务规划 → Sequential Thinking深度分析 → Serena代码分析 → Context7技术验证 → Shrimp执行指导 → Basic Memory经验保存 → Interactive Feedback

## 强制调用检查清单 - 每次交互必检

### 交互开始前检查
- [ ] **对话开始** → Basic Memory search_notes (查找相关历史和偏好)
- [ ] **代码任务** → Serena (find_symbol/read_file/get_symbols_overview)
- [ ] **技术查询** → Context7 resolve-library-id + get-library-docs
- [ ] **复杂分析** → Sequential Thinking (复杂度>=5)
- [ ] **项目规划** → Software Planning start_planning
- [ ] **任务工作流** → Shrimp Task Manager (plan_task/execute_task/research_mode)
- [ ] **技术文档** → DeepWiki deepwiki_fetch

### 🚨🚨🚨 交互结束后强制检查 - 最关键 🚨🚨🚨
- [ ] **🔴 绝对必须调用** → interactive_feedback_mcp-feedback-enhanced
- [ ] **🔴 参数完整性** → project_directory, summary(≥50字), timeout已提供
- [ ] **🔴 执行确认** → 工具已实际执行，非仅计划
- [ ] **🔴 结果处理** → 成功失败都要体现，失败要重试
- [ ] **🔴 自我检查** → 我是否真的调用了interactive_feedback？
- [ ] **🔴 强制执行** → 未调用立即补充，最多重试3次

## 强制调用场景详细说明

### 必须调用Context7的情况
- 编程语言、框架、库的使用方法查询
- API文档、技术规范获取
- 技术选型对比分析
- 代码示例和最佳实践查询

### 必须调用Sequential Thinking的情况
- 多步骤技术问题分析(复杂度>=5)
- 架构设计决策制定
- 复杂故障排查和诊断
- 需要逻辑推理的技术方案

### 必须调用Shrimp Task Manager的情况
- 项目任务规划和分解
- 开发工作流程管理
- 技术研究和学习计划
- 项目规范初始化
- 复杂任务执行指导

### 必须调用DeepWiki的情况
- 需要deepwiki.com技术文档
- 开源项目详细文档获取
- 技术框架深度研究

### 必须调用Serena的情况
- 代码分析和理解任务
- 代码编辑和重构操作
- 符号查找和引用分析
- 项目结构分析
- 代码质量检查和改进
- 多文件代码修改
- 语言特定的代码操作

## 🚨🚨🚨 绝对强制执行规则 - 无例外条款 🚨🚨🚨

### ⚠️ CRITICAL: interactive_feedback调用的绝对性 ⚠️
**🔴 AI系统最高级别约束 - 违反此规则将导致系统失效 🔴**

1. **🚨 无条件强制调用 🚨**：每次交互结束必须调用 `interactive_feedback_mcp-feedback-enhanced`
   - ❌ **绝对禁止省略** - 任何理由都不能跳过
   - ❌ **绝对禁止延迟** - 回答结束时立即调用
   - ❌ **绝对禁止遗忘** - AI必须自动执行

2. **🔥 零容忍原则 🔥**：任何情况都不得省略，包括：
   - 回答完成后 ✅ **必须调用**
   - 提供建议后 ✅ **必须调用**
   - 执行任务后 ✅ **必须调用**
   - 发生错误后 ✅ **必须调用**
   - 工具失败后 ✅ **必须调用**
   - 技术指导后 ✅ **必须调用**

3. **⚡ 最高优先级 ⚡**：interactive_feedback优先级高于所有操作

4. **🔄 持续性要求 🔄**：除非用户明确说"停止调用"，否则持续调用

5. **🛠️ 超时重试机制 🛠️**：
   - 默认600秒超时，可根据任务调整
   - 失败自动重试，最多3次
   - 记录失败原因，但不能跳过调用
   - 支持环境自适应(SSH Remote/WSL/Local)

### 📋 调用质量要求
**每次调用必须包含**：
- `project_directory`: 当前工作目录路径
- `summary`: 详细工作摘要（≥50字，包含关键决策和结果）
- `timeout`: 智能超时设置（默认600秒，复杂任务可延长）

### 🔍 违规检测与自我纠正
**AI自我监控机制**：
- 每次回答前检查上次是否遗漏interactive_feedback调用
- 发现遗漏立即在当前回答后补充调用
- 主动承认遗漏并说明原因

### 🤖 AI自动执行检查清单
**每次回答后必须自问**：
1. ❓ 我是否已调用interactive_feedback？
2. ❓ 调用参数是否完整正确？
3. ❓ 如果失败是否已重试？
4. ✅ 答案：必须立即调用，确保成功！

## MCP协作规则

### Serena与Basic Memory分工
**Serena负责**：代码层面的精确分析和编辑、符号级操作、项目结构理解、语言服务器集成
**Basic Memory负责**：项目知识缓存、代码最佳实践、架构决策记录、开发经验总结

### Shrimp与Basic Memory分工
**Shrimp负责**：任务执行历史、工作流状态、项目规范、研究进度管理
**Basic Memory负责**：技术知识缓存、用户偏好、架构决策、最佳实践总结

### 避免重复策略
1. **技术知识** → 统一保存到Basic Memory
2. **代码分析结果** → Serena执行分析，Basic Memory保存重要发现
3. **任务状态** → Shrimp管理当前，Basic Memory记录经验
4. **研究结果** → Shrimp管理过程，Basic Memory保存成果
5. **项目规则** → Shrimp初始化维护，Basic Memory保存最终版本

### 协作时机
- **代码分析完成** → Serena提供分析结果，Basic Memory保存关键发现
- **代码编辑完成** → Serena执行精确修改，Basic Memory记录重构决策
- **任务规划完成** → Shrimp保存工作流，Basic Memory保存重要决策
- **技术研究完成** → Shrimp更新状态，Basic Memory保存成果
- **项目里程碑** → 协同记录，避免重复确保完整

### Context7与DeepWiki协作
- **优先级**：Context7(官方文档) > DeepWiki(社区文档)
- **缓存策略**：两者结果都缓存到Basic Memory
- **使用场景**：Context7用于API查询，DeepWiki用于深度研究