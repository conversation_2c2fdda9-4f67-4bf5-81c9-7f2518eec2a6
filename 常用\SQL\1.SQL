SELECT
    id as mysql_id,
    country,
    city,
    uri,
    session_id,
    web_client as browser,
    reptile_type as crawler,
    if_boot as iscrawler,
    http_cookie,
    http_referer,
    http_user_agent,
    ip as remote_addr,
    url as request_uri,
    DATE_FORMAT (request_time, '%d/%b/%Y:%T') as time_local
FROM
    sta_nginx_log_day_stat
WHERE
    request_time <= "2024-09-30 00:00:00"
    and id > ?
ORDER BY
    id

三个角色：老Logstore，新（原始nginx日志），增强（+了新字段）
1. 运维停止“老Logstor”的“期刊门户日志的采集”，其他项目的采集依旧。 2025-07-21 16:02
2. 运维“数据加工”将老Logstore的数据全部加工一遍。2024-05-17 08:00:00，截止到：2025-07-21 16:02
3. 运维“数据加工”源Logstore从“老Logstore 切换到 新Logstore”。 切换开始时间点：2025-07-21 16:02
4. 崔昊将mysql存储的nignx日志，迁移到“增强的Logstore”，截止到 2024-05-17 08:00:00