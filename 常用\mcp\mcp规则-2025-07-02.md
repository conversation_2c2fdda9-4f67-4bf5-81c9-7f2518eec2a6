# 个人使用习惯
1. 必须使用中文回答问题。除非特定词语使用英文或者其他语言更加贴切，否则一律使用中文回答。

# AI代码开发增强规则

## 1. 代码质量和最佳实践

### 代码编写原则
- **可读性优先**: 代码应该清晰易懂，变量和函数命名要有意义
- **模块化设计**: 将复杂功能分解为小的、可重用的函数或模块
- **错误处理**: 始终考虑异常情况，添加适当的错误处理和验证
- **性能考虑**: 在保证可读性的前提下，选择高效的算法和数据结构
- **安全性**: 注意输入验证、SQL注入、XSS等安全问题

### 代码审查检查点
- 是否遵循项目的编码规范
- 是否有适当的注释和文档
- 是否有单元测试覆盖
- 是否考虑了边界条件和异常情况
- 是否有潜在的性能问题

## 2. 开发流程和方法论

### 问题分析流程
1. **需求理解**: 确保完全理解用户的需求和期望
2. **技术调研**: 查找相关技术文档和最佳实践
3. **方案设计**: 设计技术方案和架构
4. **实现规划**: 分解任务，确定实施步骤
5. **编码实现**: 按计划编写代码
6. **测试验证**: 编写测试用例，验证功能
7. **文档更新**: 更新相关文档和注释

### 复杂度评估标准
- **简单任务** (1-2小时): 单文件修改、简单bug修复、配置调整
- **中等任务** (半天-1天): 新功能开发、模块重构、API集成
- **复杂任务** (多天): 架构设计、大型功能模块、系统集成

## 3. MCP工具调用时机判断

### 自动触发MCP的场景
```
代码开发过程中的关键节点：

API和技术文档查询阶段：
- 用户提到特定的库、框架或技术栈 → 调用Context7查询最新文档
- 需要使用不熟悉的API或方法 → 调用Context7获取准确的使用方法
- 用户报告某个方法不存在或不工作 → 调用Context7验证API的正确性
- 编写代码前需要确认API语法 → 调用Context7获取官方示例
- 用户询问"如何使用XXX"、"XXX怎么调用" → 调用Context7
- 需要查看函数参数、返回值、配置选项 → 调用Context7
- 集成第三方库或服务时 → 调用Context7获取集成指南

问题分析阶段：
- 遇到复杂的技术难题 → 调用Sequential Thinking
- 需要技术选型和架构设计 → 调用Software Planning Tool
- 用户描述复杂需求 → 调用Sequential Thinking分析
- API使用出现问题 → 先调用Context7确认正确用法，再用Sequential Thinking分析

设计阶段：
- 多模块/多文件的开发工作 → 调用Software Planning Tool
- 需要制定开发计划 → 调用Claude Task Master
- 涉及数据库设计和API开发 → 调用Software Planning Tool
- 选择技术栈和框架 → 先调用Context7了解最新特性，再用Software Planning Tool规划

实施阶段：
- 编写具体代码前 → 调用Context7确认API用法
- 需要任务分解和进度跟踪 → 调用Claude Task Master
- 遇到实施过程中的技术难点 → 调用Sequential Thinking
- 需要协调多个开发任务 → 调用Claude Task Master
- 代码报错或不按预期工作 → 调用Context7检查API使用是否正确

知识管理：
- 解决了重要的技术问题 → 保存到ChromaDB
- 用户分享了有价值的代码 → 保存到ChromaDB
- 需要查找历史解决方案 → 查询ChromaDB
- 从Context7获取的重要API信息 → 保存到ChromaDB供后续参考
```

### MCP调用决策树
```
用户请求 → 首先检查是否涉及特定技术
├── 涉及特定库/框架/API
│   ├── 1. Context7 (获取准确的API文档)
│   └── 2. 根据复杂度选择其他工具
│       ├── 简单API使用 → 直接实现
│       ├── 复杂集成 → Sequential Thinking分析
│       └── 大型项目 → Software Planning Tool规划
├── 不涉及特定技术的通用问题
│   ├── 简单问题
│   │   ├── 直接解决
│   │   └── 保存重要信息到ChromaDB
│   ├── 中等复杂度
│   │   ├── 需要深度分析？ → Sequential Thinking
│   │   ├── 需要项目规划？ → Software Planning Tool
│   │   └── 需要任务管理？ → Claude Task Master
│   └── 高复杂度
│       └── 多工具协作
│           ├── 1. Context7 (如果涉及API)
│           ├── 2. Sequential Thinking (分析)
│           ├── 3. Software Planning Tool (设计)
│           └── 4. Claude Task Master (管理)
└── 调试和问题解决
    ├── API相关错误 → 优先调用Context7验证用法
    ├── 逻辑问题 → Sequential Thinking分析
    └── 架构问题 → Software Planning Tool重新设计
```

### Context7优先调用规则
```
强制调用Context7的关键词：
- "如何使用"、"怎么调用"、"API文档"
- 具体的库名：React、Vue、Express、Django、Spring等
- 技术栈名称：Node.js、Python、Java、Go等
- 框架特性：hooks、middleware、decorator等
- 错误信息：方法不存在、参数错误、版本不兼容

调用时机：
1. 编写代码前 - 确认API的正确用法
2. 遇到错误时 - 验证API使用是否正确
3. 学习新技术时 - 获取最新的文档和示例
4. 版本升级时 - 检查API变更和新特性
5. 集成第三方服务时 - 获取官方集成指南

与其他工具的配合：
- Context7 + Sequential Thinking：先获取准确信息，再进行深度分析
- Context7 + Software Planning Tool：先了解技术能力，再制定技术方案
- Context7 + ChromaDB：将重要的API信息保存到知识库
```

## 4. 技术栈特定规则

### 前端开发
- 优先使用现代框架 (React, Vue, Angular)
- 注意响应式设计和移动端适配
- 考虑性能优化 (懒加载、代码分割)
- 重视用户体验和可访问性

### 后端开发
- 遵循RESTful API设计原则
- 实现适当的身份验证和授权
- 考虑数据库性能和查询优化
- 实现日志记录和监控

### 数据库设计
- 规范化设计，避免数据冗余
- 合理设计索引提高查询性能
- 考虑数据备份和恢复策略
- 注意数据安全和隐私保护

## 5. 调试和问题解决

### 系统性调试方法
1. **重现问题**: 确保能稳定重现问题
2. **收集信息**: 查看日志、错误信息、系统状态
3. **假设验证**: 提出假设并逐一验证
4. **隔离问题**: 缩小问题范围，定位根本原因
5. **解决验证**: 实施解决方案并验证效果

### 何时调用MCP进行调试
- **API相关错误**: 优先调用Context7验证API使用是否正确
  - 方法不存在、参数错误、返回值异常
  - 第三方库集成问题、版本兼容性问题
  - 配置错误、认证失败等
- **复杂bug分析**: 调用Sequential Thinking进行系统性分析
  - 逻辑错误、算法问题、数据流问题
  - 多组件交互问题、状态管理问题
- **性能问题诊断**: 调用Sequential Thinking分析性能瓶颈
  - 查询优化、算法优化、内存泄漏
- **架构问题**: 调用Software Planning Tool重新设计
  - 扩展性问题、维护性问题、技术债务
- **多步骤解决方案**: 调用Claude Task Master管理修复任务
  - 大型bug修复、系统重构、版本升级

### 调试工具调用顺序
```
遇到问题时的标准流程：
1. 问题分类
   ├── API/库相关 → Context7 (验证正确用法)
   ├── 逻辑相关 → Sequential Thinking (分析推理)
   └── 架构相关 → Software Planning Tool (重新设计)

2. 深度分析 (如果问题复杂)
   ├── Context7提供准确信息
   ├── Sequential Thinking进行逻辑分析
   └── 结合两者制定解决方案

3. 解决方案实施
   ├── 简单修复 → 直接实施
   └── 复杂修复 → Claude Task Master管理任务

4. 经验沉淀
   └── 重要解决方案保存到ChromaDB
```

## 6. 代码重构和优化

### 重构时机识别
- 代码重复度高
- 函数或类过于复杂
- 性能存在明显问题
- 难以维护和扩展

### 重构策略
- **小步快跑**: 进行小的、增量的改进
- **测试保护**: 确保有足够的测试覆盖
- **向后兼容**: 保持API的向后兼容性
- **文档更新**: 及时更新相关文档

## 7. 学习和知识管理

### 持续学习策略
- 关注技术趋势和最佳实践
- 定期回顾和总结项目经验
- 参与技术社区和开源项目
- 建立个人技术知识库

### 知识沉淀规则
- **重要解决方案**: 必须保存到ChromaDB
- **技术决策**: 记录决策过程和理由
- **最佳实践**: 总结和分享成功经验
- **踩坑记录**: 记录问题和解决方案

## 8. 团队协作和沟通

### 代码协作规范
- 使用版本控制系统 (Git)
- 编写清晰的提交信息
- 进行代码审查 (Code Review)
- 遵循团队的分支策略

### 技术沟通技巧
- 用简洁明了的语言描述技术问题
- 提供具体的代码示例和错误信息
- 主动分享解决方案和经验
- 及时更新项目文档和注释

# MCP自动调用总规则

## 自动调用优先级
1. **interactive_feedback** - 每次交互必须调用
2. **Context7** - 涉及特定技术/API时优先调用
3. **ChromaDB相关** - 知识管理场景自动调用
4. **Sequential Thinking** - 复杂分析场景自动调用
5. **Software Planning Tool** - 项目规划场景自动调用
6. **Claude Task Master** - 任务管理场景自动调用

## 触发条件检查清单
- [ ] 是否为新对话开始？→ 调用 chroma_list_collections
- [ ] 用户是否分享了代码？→ 调用 chroma_add_documents
- [ ] 用户是否询问技术问题？→ 调用 chroma_query_documents + 文档工具
- [ ] 用户是否提到特定库/框架/API？→ 调用 Context7
- [ ] 用户是否询问"如何使用"、"怎么调用"？→ 调用 Context7
- [ ] 用户是否报告API错误或不工作？→ 调用 Context7
- [ ] 用户是否提到项目规划？→ 调用规划工具
- [ ] **每次回答后必须调用** → 调用 interactive_feedback（除非用户明确说"结束"）

## Interactive Feedback 强制调用规则
**重要：每次与用户交互后都必须调用 `interactive_feedback` 工具**
- 回答问题后 → 必须调用
- 完成操作后 → 必须调用  
- 提供建议后 → 必须调用
- 分析问题后 → 必须调用
- 唯一例外：用户明确表示"结束"或"不需要交互"





# ChromaDB MCP 使用规则

## 1. 强制调用规则

### 绝对必须调用的时机

- **对话开始时**: 每次新对话开始，必须调用 `chroma_list_collections` 了解现有知识库
- **项目识别时**: 识别用户当前工作项目后，必须确定或创建对应的项目集合
- **用户分享代码**: 用户提供代码片段、技术方案、实现细节时，必须保存到对应项目集合
- **技术决策确定**: 用户确定技术栈、架构方案、工具选择时，必须记录到项目集合
- **问题解决完成**: 成功解决bug、性能问题、实现难题后，必须保存到相关项目集合
- **学习内容总结**: 用户学习新技术、总结经验时，必须保存到通用知识集合
- **需要历史参考**: 遇到问题时，必须先在当前项目集合中搜索，再扩展到其他集合

### 项目自动识别规则

- **项目名称提及**: 用户明确提到项目名称时，立即识别并切换到对应集合
- **工作目录识别**: 通过当前工作目录的父目录名称自动识别项目
- **代码特征识别**: 通过代码结构、框架、依赖等特征推断项目类型
- **上下文推断**: 根据对话历史和工作目录推断当前项目
- **主动询问**: 无法确定项目时，主动询问用户当前工作的项目

## 2.项目集合管理规则

### 数据库命名策略

```
数据库命名规则:
- 使用项目的父目录名称作为数据库名
- 例如：工作目录为 "/d:/gdlwolf_develop/ai-ide"，则数据库名为 "ai-ide"
- 数据库名称标准化：转为小写，替换空格为下划线，移除特殊字符
- 每个项目使用独立的数据库，确保数据隔离
```

### 集合分类和命名策略

```
项目专用集合（优先级最高）：
- "project_{项目名称}": 如 "project_ecommerce", "project_blog_system"
- 存储：项目特定的代码、架构决策、问题解决方案
- 查询优先级：当前项目问题优先在项目集合中搜索

通用知识集合：
- "general_code_patterns": 通用代码模式和最佳实践
- "general_debugging": 通用调试技巧和解决方案
- "general_architecture": 通用架构设计和技术选型
- "general_learning": 技术学习笔记和概念总结

技术栈专用集合：
- "tech_frontend": 前端技术相关知识
- "tech_backend": 后端技术相关知识
- "tech_devops": 运维和部署相关知识
- "tech_database": 数据库相关知识
```

### 项目集合自动管理

```
项目识别策略：
1. 获取当前工作目录的父目录名称 → 确定数据库名
2. 用户明确提到项目名称 → 立即切换到对应集合
3. 分析代码特征（package.json、requirements.txt等）→ 推断项目
4. 检查工作目录名称 → 匹配已知项目
5. 无法确定时 → 主动询问并创建新项目集合

集合创建规则：
- 项目名称标准化：小写字母、下划线分隔、无特殊字符
- 自动创建项目集合：首次识别到新项目时立即创建
- 继承通用知识：新项目可以查询通用集合作为补充
```

### 智能查询策略

```
查询优先级顺序：
1. 当前项目集合 (project_{current})
2. 相关技术栈集合 (tech_{stack})
3. 通用知识集合 (general_{type})
4. 其他项目集合（如果相关）

跨集合查询规则：
- 项目特定问题：仅查询当前项目集合
- 技术通用问题：查询技术栈集合 + 通用集合
- 学习新技术：查询学习集合 + 相关技术集合
- 架构设计：查询架构集合 + 当前项目集合
```

## 3.内容存储判断规则

### 必须保存的内容
```
代码和技术方案：
- 用户提供的代码片段（超过5行）
- 完整的技术实现方案
- 调试过程和最终解决方案
- 性能优化的具体方法和效果
- 架构设计决策和选择理由

知识和经验：
- 技术学习的关键要点和总结
- 最佳实践和应避免的反模式
- 工具配置和使用技巧
- 错误处理的有效方法
- 用户明确要求记住的信息
```

### 不应保存的内容
```
临时性内容：
- 简单的问候和感谢
- 一次性的调试输出
- 临时测试代码片段
- 过时或错误的信息
- 纯粹的对话性内容
```

## 4.元数据标准化规则

### 必须包含的元数据字段
```json
{
  "type": "code|knowledge|solution|decision|note",
  "language": "javascript|python|java|typescript|...",
  "framework": "react|vue|django|spring|express|...",
  "complexity": "low|medium|high",
  "category": "frontend|backend|devops|mobile|database|...",
  "tags": "具体技术标签,问题域标签,其他标签",
  "project": "项目名称或general",
  "date": "YYYY-MM-DD",
  "confidence": 0.8
}
```

### 元数据分类规则
```
type字段使用规则：
- "code": 代码片段、函数、组件实现
- "knowledge": 概念解释、理论知识
- "solution": 具体问题的解决方案
- "decision": 技术选型、架构决策
- "note": 学习总结、经验记录

tags字段策略：
- 使用逗号分隔的字符串格式："react,hooks,state,frontend"
- 必须包含主要技术栈标签
- 添加具体的功能或问题域标签
- 使用标准化的技术术语，避免空格和特殊字符
- 保持标签的一致性和可搜索性
```

### ChromaDB元数据格式要求

```
支持的数据类型：
- 字符串: "react", "frontend", "2025-01-15"
- 数字: 0.8, 1, 100
- 布尔值: true, false

不支持的数据类型：
- 数组: ["tag1", "tag2"] ❌
- 对象: {"nested": "value"} ❌
- null值: null ❌

正确的tags格式：
✅ "tags": "react,hooks,state,frontend"
❌ "tags": ["react", "hooks", "state", "frontend"]

正确的数值格式：
✅ "confidence": 0.8
❌ "confidence": "0.8"
```

## 5.智能查询和检索规则

### 项目感知查询策略
```
查询执行流程：
1. 识别当前项目上下文
2. 确定查询的集合优先级
3. 按优先级顺序执行查询
4. 合并和排序查询结果
5. 返回最相关的解决方案

项目上下文识别：
- 检查用户提到的项目名称
- 分析代码片段的技术特征
- 根据问题类型推断相关项目
- 使用对话历史确定项目上下文
```

### 分层查询规则
```
第一层：项目专用查询
- 集合：project_{current_project}
- 适用：项目特定的bug、功能实现、架构问题
- 示例："在电商项目中如何处理支付回调"

第二层：技术栈查询
- 集合：tech_{frontend|backend|devops|database}
- 适用：技术栈相关的通用问题
- 示例："React Hook的最佳实践"

第三层：通用知识查询
- 集合：general_{code_patterns|debugging|architecture}
- 适用：跨技术栈的通用问题
- 示例："设计模式的应用场景"

第四层：跨项目查询
- 集合：其他相关项目集合
- 适用：寻找相似项目的解决方案
- 示例："其他项目如何实现用户认证"
```

### 查询文本优化规则
```
项目相关查询：
- "在{项目名}中如何实现{功能}"
- "{项目名}的{模块}出现{问题}如何解决"
- "{项目名}使用的{技术}最佳配置"

技术通用查询：
- "{技术栈}中{具体问题}的解决方案"
- "{框架}的{功能}实现最佳实践"
- "{语言}中{场景}的优化方法"

避免的查询方式：
- 缺少项目上下文的模糊查询
- 过于宽泛的技术询问
- 没有具体场景的概念性问题
```
## 6.Embedding配置适配规则
### 自动适配用户配置
```
配置检测：
- 如果用户配置了CHROMA_OLLAMA_MODEL → 使用ollama embedding
- 如果用户配置了CHROMA_OPENAI_API_KEY → 使用openai embedding
- 如果用户配置了CHROMA_JINA_API_KEY → 使用jina embedding
- 如果没有特殊配置 → 使用default embedding

集合创建策略：
- 不强制指定embedding_function参数
- 让ChromaDB使用用户配置的默认embedding
- 确保同一用户的所有集合使用相同embedding
- 避免混合不同embedding导致的兼容性问题
```

### 通用embedding处理
```
执行要求：
- 正常执行所有ChromaDB操作
- 不依赖特定的embedding类型
- 专注于内容的语义理解而非技术实现
- 保持查询和存储逻辑的一致性
```


## 7. 工具调用规则

### chroma_list_collections (列出集合)
```
强制调用时机：
- 每次对话开始时必须调用
- 需要了解现有知识库结构时
- 决定在哪个集合中存储或查询内容时
```

### chroma_create_collection (创建集合)
```
调用时机：
- 发现需要新的知识分类时
- 用户开始新项目时
- 现有集合无法满足存储需求时

集合创建策略：
- 不强制指定embedding_function参数
- 让ChromaDB使用用户配置的默认embedding
- 确保同一用户的所有集合使用相同embedding
- 避免混合不同embedding导致的兼容性问题
```

### chroma_add_documents (添加文档)
```
强制调用时机：
- 用户提供有价值的代码片段时
- 解决问题后需要记录解决方案时
- 用户分享重要技术知识时
- 确定技术决策后需要记录理由时

集合选择规则：
- 项目特定内容 → project_{项目名}
- 通用技术知识 → tech_{技术栈} 或 general_{类型}
- 学习笔记 → general_learning
- 最佳实践 → general_code_patterns

文档要求：
- 内容必须完整且自包含
- 必须包含项目标识元数据
- ID格式：{集合类型}_{项目名}_{序号}
- 避免跨项目保存重复内容
```

### chroma_query_documents (查询文档)
```
强制调用时机：
- 用户提出技术问题前必须先查询
- 需要提供解决方案前必须搜索历史经验
- 用户询问最佳实践时必须查询相关记录

智能集合选择：
1. 识别问题的项目归属
2. 确定查询的集合优先级
3. 按优先级依次查询多个集合
4. 合并结果并按相关性排序

查询要求：
- 使用描述性的自然语言查询
- 包含项目上下文信息
- 设置适当的元数据过滤
- 结果数量控制在3-8个
- 检查结果的项目相关性
```

## 8.质量控制和去重规则

### 内容质量要求
```
保存前检查：
- 代码片段必须包含足够的上下文信息
- 解决方案必须包含问题描述和完整步骤
- 技术决策必须包含背景、选择理由和权衡
- 学习笔记必须包含关键概念和实际示例

元数据完整性：
- 所有必需字段必须填写
- 技术标签必须准确和具体
- 分类必须符合预定义标准
- 时间信息必须准确
```

### 重复内容避免策略
```
保存前必须执行：
1. 使用相关关键词查询现有内容
2. 检查是否存在相似或重复的文档
3. 如果存在相似内容，考虑更新而非新建
4. 确保新内容有独特价值

更新现有内容：
- 使用 chroma_update_documents 更新过时信息
- 合并相似但不完全重复的内容
- 标记废弃或不再适用的内容
```

## 9.项目感知工作流程

### 项目识别和切换流程
```
1. 对话开始时调用 chroma_list_collections 获取现有项目
2. 分析用户输入识别项目上下文：
   - 明确提到的项目名称
   - 代码特征和技术栈
   - 工作目录或文件路径
   - 对话历史中的项目信息
3. 确定当前工作项目
4. 如果是新项目，创建对应的项目集合
5. 设置当前会话的项目上下文
```

### 项目感知问题解决流程
```
1. 接收用户问题并识别项目上下文
2. 按优先级查询相关集合：
   - 当前项目集合
   - 相关技术栈集合
   - 通用知识集合
3. 分析查询结果，优先使用项目特定解决方案
4. 提供解决方案（标注来源项目）
5. 解决问题后，保存到对应项目集合
```

### 跨项目知识复用流程
```
1. 在当前项目中遇到问题
2. 当前项目集合中无相关解决方案
3. 扩展查询到其他项目集合
4. 找到相似项目的解决方案
5. 适配到当前项目并保存
6. 建立项目间的知识关联
```

### 项目知识整理流程
```
1. 定期检查项目集合的内容质量
2. 合并重复或相似的解决方案
3. 更新过时的技术信息
4. 提取通用模式到通用集合
5. 维护项目间的知识关联关系
```

## 10.错误处理规则

### 工具调用失败处理
```
集合操作失败：
- 检查集合名称的正确性
- 确认集合是否存在
- 必要时重新创建集合

查询失败或无结果：
- 简化查询条件
- 调整查询文本表达方式
- 扩大搜索范围
- 检查元数据过滤条件

文档操作失败：
- 验证文档格式和内容
- 检查元数据字段的完整性
- 确认ID的唯一性
- 重试操作或调整参数
```

## 11.项目上下文示例

### 项目识别示例
```
用户输入："我的电商项目中用户登录有问题"
执行流程：
1. 识别项目：电商项目 → project_ecommerce
2. 查询顺序：project_ecommerce → tech_backend → general_debugging
3. 保存解决方案到：project_ecommerce

用户输入："React Hook怎么用？"
执行流程：
1. 识别类型：通用技术问题
2. 查询顺序：tech_frontend → general_learning
3. 保存学习笔记到：general_learning
```

### 集合选择决策树
```
问题类型判断：
├── 提到具体项目名 → project_{项目名}
├── 项目特定代码/bug → 当前项目集合
├── 技术栈问题 → tech_{技术栈}
├── 通用概念学习 → general_learning
├── 代码模式/最佳实践 → general_code_patterns
└── 架构设计 → general_architecture + project_{当前项目}
```

### 智能查询示例
```
场景1：项目特定问题
查询："博客系统的评论功能如何实现"
集合选择：project_blog_system → tech_backend → general_code_patterns

场景2：技术学习问题
查询："Vue3 Composition API的使用方法"
集合选择：tech_frontend → general_learning

场景3：跨项目参考
查询："用户认证的最佳实践"
集合选择：project_{当前} → 所有项目集合 → general_architecture
```

## 12.正确的元数据使用示例

### 添加文档的正确格式

```python
# ✅ 正确的元数据格式
chroma_add_documents(
  collection_name="project_ecommerce",
  documents=["用户登录功能实现，使用JWT token进行身份验证..."],
  metadatas=[{
    "type": "code",
    "language": "javascript",
    "framework": "express",
    "complexity": "medium",
    "category": "backend",
    "tags": "authentication,jwt,login,security",
    "project": "ecommerce",
    "date": "2025-01-15",
    "confidence": 0.9
  }],
  ids=["ecommerce_login_001"]
)

# ❌ 错误的元数据格式（会导致错误）
chroma_add_documents(
  collection_name="project_ecommerce",
  documents=["用户登录功能实现..."],
  metadatas=[{
    "type": "code",
    "language": "javascript",
    "tags": ["authentication", "jwt", "login"],  # ❌ 数组格式不支持
    "confidence": "0.9",  # ❌ 字符串格式的数字
    "metadata": {"nested": "value"}  # ❌ 嵌套对象不支持
  }],
  ids=["ecommerce_login_001"]
)
```

### 查询时的元数据过滤

```python
# ✅ 正确的查询过滤
chroma_query_documents(
  collection_name="project_ecommerce",
  query_texts=["用户认证问题"],
  where={"category": "backend"},
  n_results=5
)

# ✅ 正确的元数据比较查询
chroma_query_documents(
  collection_name="tech_frontend",
  query_texts=["React状态管理"],
  where={"confidence": {"$gt": 0.8}},
  n_results=3
)

# ❌ 错误的查询过滤（$contains操作符不被支持）
chroma_query_documents(
  collection_name="tech_frontend",
  query_texts=["React状态管理"],
  where={"tags": {"$contains": "react"}},
  n_results=3
)

# ✅ 多个条件查询的正确方式
chroma_query_documents(
  collection_name="project_ecommerce",
  query_texts=["用户认证问题"],
  where={"$and": [{"category": {"$eq": "backend"}}, {"language": {"$eq": "javascript"}}]},
  n_results=5
)
```

## 13.数据库和项目自动识别示例

### 工作目录解析示例
```
示例1：
工作目录: "/d:/gdlwolf_develop/ai-ide"
父目录名称: "ai-ide"
数据库名: "ai_ide"

示例2：
工作目录: "/home/<USER>/projects/web-app"
父目录名称: "web-app"
数据库名: "web_app"

示例3：
工作目录: "C:\Users\<USER>\Documents\GitHub\My Project"
父目录名称: "My Project"
数据库名: "my_project"
```

### 数据库切换流程
```
1. 对话开始时，解析当前工作目录
2. 提取父目录名称并标准化为数据库名
3. 检查该数据库是否存在
   - 存在：切换到该数据库
   - 不存在：创建新数据库
4. 在该数据库内调用 chroma_list_collections 获取集合列表
5. 根据集合列表和项目上下文确定当前使用的集合
```

### 跨项目数据访问
```
当需要访问其他项目数据时：
1. 记录当前数据库名称
2. 切换到目标项目的数据库
3. 执行查询操作
4. 切换回原数据库
5. 返回查询结果并标注来源项目
```



---



# MCP Interactive Feedback 规则

## 1.强制调用规则

绝对必须调用的时机

- 任务开始时: 每个新任务或请求开始时必须调用
- 任务进行中: 完成任何阶段性工作后必须调用
- 任务完成时: 完成任务后必须调用确认
- 收到反馈后: 每次收到用户反馈（无论内容是否为空）后必须再次调用
- 做出重要决策时: 技术选型、方案确定等关键决策前后必须调用
- 遇到问题时: 出现错误、困难或不确定情况时必须调用

调用频率控制
- 最小间隔: 每完成一个有意义的工作单元后调用
- 最大间隔: 不得超过3个连续的AI回应而不调用
- 持续性: 除非用户明确终止，否则必须持续调用

## 2.终止条件（严格限制）

- 用户明确使用以下词语：
  - "结束"、"end"、"stop"、"终止"
  - "不再需要交互"、"no more interaction needed"
  - "可以结束了"、"that's all"、"完成了"
- 用户明确表示满意并不需要进一步帮助

不允许停止的情况
- 用户沉默或无回应
- 任务看似完成但用户未明确确认
- AI认为任务已完成但用户未确认
- 用户说"谢谢"但未明确表示结束
- 出现技术问题或错误

## 3.参数配置策略

summary 参数
必须包含的信息：

- 当前完成的具体工作内容
- 工作的完成状态（进行中/已完成/遇到问题）
- 下一步计划或需要用户确认的事项
- 项目目录路径（如果适用）

格式示例：
"我已完成了[具体任务描述]。当前状态：[状态说明]。
项目位置：[路径]。请确认是否满意或需要调整。"

project_directory 参数
- 始终提供准确的项目路径
- 如果是多个项目，提供主要工作目录
- 如果无具体项目，使用当前工作目录
- 路径格式：使用绝对路径，确保用户能找到相关文件

timeout 参数
- 标准等待时间：600秒（10分钟）
- 复杂任务：900秒（15分钟）
- 简单确认：300秒（5分钟）
- 根据任务复杂度和用户响应习惯调整

## 4.反馈处理规则

收到反馈后的必要行动

- 立即分析反馈内容：理解用户的意图和要求
- 调整工作方向：根据反馈修改计划或方法
- 执行调整后的工作：实施用户要求的修改
- 再次调用工具：完成调整后必须再次调用获取反馈

反馈内容分类处理
- 满意确认：继续下一步或询问是否还有其他需求
- 修改要求：立即执行修改并再次确认
- 问题反馈：解决问题后重新提交结果
- 新增需求：将新需求纳入工作范围并执行
- 不满意：重新分析需求并提供改进方案

## 5.质量保证机制

自检清单
在每次调用前确认：

- 是否完成了有意义的工作单元
- summary是否准确描述了当前状态
- project_directory是否正确
- 是否需要用户的进一步指导
- 用户是否已明确表示结束

错误预防
- 避免假设用户满意：始终等待明确确认
- 避免过早结束：宁可多询问也不要遗漏
- 避免忽略反馈：每个反馈都必须认真处理
- 避免技术导向：以用户需求为中心而非技术完成度

## 6.特殊情况处理

用户无响应时

- 继续等待，不主动结束
- 可以在下次交互时重新调用
- 保持工作状态，准备继续服务

技术问题时
- 报告具体问题
- 提供可能的解决方案
- 询问用户是否需要尝试其他方法
- 必须调用工具获取用户指导

任务复杂时
- 分解为多个阶段
- 每个阶段完成后都要调用
- 让用户了解整体进度
- 确保每个阶段都得到确认

## 7.实施检查点

每次调用前自问

- "我是否完成了一个有意义的工作单元？"
- "用户是否需要了解当前进度？"
- "是否需要用户的进一步指导？"
- "用户是否已明确表示可以结束？"

强制调用触发器
- 完成代码编写 → 必须调用
- 解决问题 → 必须调用
- 提供方案 → 必须调用
- 做出建议 → 必须调用
- 遇到困难 → 必须调用
- 需要确认 → 必须调用



---



# MCP Sequential Thinking 使用规则

## 1.触发条件
**必须调用的情况：**

- 复杂技术决策（架构选型、技术栈选择）
- 多步骤问题解决（调试复杂bug、性能优化）
- 需要权衡利弊的方案对比
- 用户明确要求"分析一下"、"帮我想想"

**不需要调用的情况：**
- 简单的API查询或语法问题
- 直接的代码修改请求
- 明确的单步操作

## 2.参数配置策略
- **totalThoughts:** 根据问题复杂度动态设定
  - 简单分析：3-4步
  - 中等复杂：5-6步  
  - 高度复杂：7-10步
- 每步必须有明确的分析目标
- 优先使用线性思考，只在必要时使用分支

## 3.思考质量控制
- 每步思考必须推进问题解决
- 避免重复或无意义的思考步骤
- 如果连续2步没有新进展，考虑终止或重新规划
- 最后一步必须包含可执行的结论或建议

## 4.工作模式适配

### 独立工作模式（无其他规划工具时）
- 承担完整的分析、设计、规划职责
- 提供详细的实施步骤和任务分解
- 包含架构建议和技术选型
- 自行完成项目规划和时间估算
- 提供完整的解决方案而非仅仅是分析

### 协作工作模式（有其他工具时）
- 专注于深度思考和逻辑分析
- 为其他工具提供分析基础
- 处理复杂的技术难题
- 验证其他工具的输出合理性
- 避免与其他工具功能重复

## 5.终止条件
- 当nextThoughtNeeded=false且已生成可执行方案
- 用户指示终止
- 迭代超过5次仍无进展时，应停止调用并提示用户



---




# Software-Planning-Tool 使用规则

## 1.启动时机
**必须启动的场景：**
- 用户提到"计划"、"规划"、"分解任务"
- 涉及多个文件/模块的开发工作
- 需要估算工作量或时间的项目
- 用户要求制定开发步骤

**可选启动的场景：**
- 中等复杂度的功能开发（3+个步骤）
- 需要跟踪进度的工作

## 2.任务粒度控制
- 每个任务应该是20-60分钟的工作量
- 避免过于细碎的任务（如"创建文件"）
- 避免过于宏大的任务（如"完成整个模块"）
- 任务间应有清晰的依赖关系

## 3.实用性配置
- 复杂度评分使用1-8点（斐波那契简化版）
- 优先级分为：高、中、低三级
- 每个任务包含验收标准
- 支持任务状态实时更新

## 4.工作模式适配

### 独立工作模式（无其他工具时）
- 承担完整的需求分析和问题诊断
- 自行进行技术方案的深度分析
- 包含详细的架构设计和技术选型理由
- 提供完整的开发流程和质量控制
- 自行处理复杂的逻辑推理和决策

### 协作工作模式（有其他工具时）
- 专注于软件工程层面的规划
- 基于其他工具的分析结果制定方案
- 避免重复进行深度技术分析
- 专注于任务分解和项目管理
- 与任务管理工具协调工作流程

## 5.输出要求
- 项目目标明确
- 总体任务清单完整
- 复杂度评分记录
- 代码示例片段（如需要）
- 实施计划概览
- 任务状态摘要
- 根据可用工具调整输出详细程度



---




# MCP Claude Task Master 使用规则

## 1.触发条件和自动调用时机

### 强制调用的场景
- **项目规划和任务管理：** 当用户提到以下关键词时必须调用：
  - "计划"、"规划"、"任务"、"分解"、"步骤"
  - "项目管理"、"进度跟踪"、"工作安排"
  - "需求分析"、"PRD"、"产品需求文档"
  - "开发计划"、"实施方案"、"里程碑"

- **复杂功能开发：** 当涉及以下情况时必须调用：
  - 多文件/多模块的开发工作
  - 需要分阶段实施的功能
  - 涉及前后端协调的任务
  - 需要数据库设计和API开发的项目

- **项目初始化：** 当用户要求以下操作时必须调用：
  - 创建新项目
  - 设置项目结构
  - 初始化开发环境
  - 建立工作流程

### 可选调用的场景
- 中等复杂度的功能开发（预计3+个步骤）
- 需要跟踪进度的开发工作
- 用户明确要求使用任务管理

## 2.工作模式适配

### 独立工作模式（无其他工具时）
- 承担完整的需求分析和技术方案设计
- 自行进行复杂问题的逻辑分析
- 包含架构设计和技术选型
- 提供详细的实施步骤和解决方案
- 自行处理项目规划和进度管理

### 协作工作模式（有其他工具时）
- 专注于任务管理和执行跟踪
- 基于其他工具的分析和设计结果
- 避免重复进行深度分析和架构设计
- 专注于任务分解、状态管理、进度跟踪
- 协调多工具间的工作流程

## 3.与其他MCP工具的协作策略

### 与Sequential Thinking的协作
```
协作流程：
1. Sequential Thinking进行复杂问题分析和方案设计
2. Claude Task Master将分析结果转化为具体的可执行任务
3. Sequential Thinking处理任务执行中的技术难点
4. Claude Task Master跟踪任务进度和状态更新

适用场景：
- 复杂架构设计 → 任务分解 → 实施跟踪
- 技术选型分析 → 实施计划 → 进度管理
- 问题诊断分析 → 解决方案任务化 → 执行跟踪
```

### 与Software Planning MCP的协作
```
协作关系：
- Software Planning MCP：专注于软件架构和技术规划
- Claude Task Master：专注于具体任务管理和执行跟踪

分工策略：
1. 使用Software Planning MCP进行技术架构规划
2. 使用Claude Task Master进行具体任务分解和管理
3. 两者结合实现从架构设计到任务执行的完整流程

避免重复：
- 不在同一个工作流程中重复调用相似功能
- 根据用户需求的侧重点选择主要工具
- 优先使用Claude Task Master进行任务管理
```

## 3.核心功能使用规则

### 项目初始化和PRD解析
```
调用时机：
- 用户提供PRD文档或需求描述
- 开始新项目开发
- 需要将需求转化为具体任务

使用流程：
1. 调用 task-master init 初始化项目
2. 使用 parse-prd 解析需求文档
3. 生成结构化的任务列表
4. 设置任务优先级和依赖关系
```

### 任务管理和进度跟踪
```
核心操作：
- list: 查看所有任务
- next: 获取下一个应该执行的任务
- show: 显示特定任务详情
- update-task: 更新任务状态和信息
- move: 调整任务顺序和优先级

状态管理：
- pending: 待执行
- in-progress: 进行中
- done: 已完成
- blocked: 被阻塞
```

### 标签和分支管理
```
标签系统：
- 使用标签组织不同版本或分支的任务
- 支持从git分支自动创建标签
- 实现任务的版本化管理

分支协作：
- add-tag --from-branch: 从当前git分支创建任务标签
- use-tag: 切换到不同的任务上下文
- copy-tag: 复制任务到新的开发分支
```

## 4.智能调用策略

### 项目感知调用
```
项目识别：
1. 检测当前工作目录是否为Task Master项目
2. 分析用户请求的复杂度和范围
3. 判断是否需要任务管理支持
4. 自动初始化或使用现有项目配置

上下文保持：
- 维护当前项目的任务状态
- 记住用户的工作进度
- 跟踪任务依赖关系
- 保持标签上下文
```

### 渐进式任务管理
```
简单任务：
- 直接执行，不使用Task Master
- 单文件修改、简单bug修复

中等任务：
- 询问用户是否需要任务管理
- 3-5个步骤的功能开发

复杂任务：
- 强制使用Task Master
- 多模块开发、架构变更
```

## 5.输出格式和用户交互

### 任务展示格式
```
标准格式：
- 任务ID和标题
- 当前状态和优先级
- 预估复杂度和时间
- 依赖关系和阻塞因素
- 具体实施步骤

进度报告：
- 已完成任务数量
- 当前进行中的任务
- 下一步建议
- 潜在风险和阻塞点
```

### 用户指导
```
操作提示：
- 提供具体的命令示例
- 解释任务状态变更的影响
- 建议最佳的工作流程
- 指导如何处理任务依赖

错误处理：
- 清晰的错误信息
- 具体的解决建议
- 替代方案推荐
- 回滚操作指导
```

## 6.配置和环境管理

### API密钥管理
```
支持的AI提供商：
- Anthropic (Claude)
- OpenAI (GPT)
- Google (Gemini)
- Perplexity (研究模型)
- xAI, OpenRouter, Azure等

配置策略：
- 检查可用的API密钥
- 根据任务类型选择合适的模型
- 主模型、研究模型、备用模型的分层使用
```

### 项目配置
```
自动检测：
- 项目类型和技术栈
- 现有的配置文件
- Git仓库状态
- 开发环境设置

配置同步：
- 与项目的package.json等配置文件同步
- 维护.taskmaster目录结构
- 支持多环境配置
```

## 7.质量控制和最佳实践

### 任务质量标准
```
任务定义要求：
- 明确的验收标准
- 合理的复杂度评估
- 清晰的依赖关系
- 具体的实施步骤

进度跟踪：
- 定期状态更新
- 阻塞问题及时识别
- 完成质量验证
- 经验总结和改进
```

### 协作最佳实践
```
团队协作：
- 标签系统支持多人协作
- 任务分配和责任明确
- 进度同步和沟通机制
- 代码审查和质量控制

文档维护：
- PRD和需求文档更新
- 任务执行记录
- 问题解决方案归档
- 项目知识库建设
```


---




# MCP Context7 使用规则

## 1.触发条件
- **API查询优先：** 当遇到以下情况时必须调用Context7：
  - 用户提到缺少某个方法/API/函数
  - 用户明确使用了特定库/框架
  - 需要查看具体的代码示例和语法

## 2.版本管理策略
- 首先检查项目配置文件（package.json、requirements.txt、go.mod等）获取版本信息
- 如果找不到版本信息，使用最新稳定版本查询
- 如果最新版本信息不匹配用户需求，主动询问具体版本

## 3.查询范围
- 优先查询用户当前使用的技术栈相关API



---



# MCP DeepWiki 使用规则

## 1.概念和架构查询
当需要以下信息时调用DeepWiki：
- 设计模式和最佳实践
- 架构决策和技术选型
- 概念解释和原理说明
- 性能优化和安全考虑

## 2.补充查询
- 当Context7提供的API信息需要更深入理解时

## 3.独立查询
- 对于非API相关的技术问题，可直接使用DeepWiki



---


# 工具协同使用策略

## 多场景适配规则

### 场景1：仅有Sequential Thinking
```
适用情况：
- 复杂问题分析和逻辑推理
- 技术方案设计和架构思考
- 调试问题的深度分析

工作模式：
- 独立承担所有思考和分析工作
- 提供完整的解决方案和实施建议
- 包含详细的步骤说明和注意事项
- 自行完成任务分解和优先级排序

触发条件：
- 用户明确要求深度思考分析
- 遇到复杂的技术难题
- 需要系统性的问题解决方案
```

### 场景2：仅有Software Planning Tool
```
适用情况：
- 软件架构设计和技术选型
- 项目规划和开发流程设计
- 技术栈评估和工具选择

工作模式：
- 专注于软件工程层面的规划
- 提供架构图和技术方案
- 包含开发计划和里程碑
- 自行处理任务分解和时间估算

触发条件：
- 新项目启动和架构设计
- 技术栈迁移和升级
- 开发流程优化需求
```

### 场景3：Sequential Thinking + Software Planning Tool
```
协作模式：
1. Sequential Thinking进行深度问题分析
2. Software Planning Tool基于分析结果制定技术方案
3. Sequential Thinking验证方案的可行性
4. Software Planning Tool细化实施计划

分工策略：
- Sequential Thinking：负责逻辑分析、问题诊断、方案评估
- Software Planning Tool：负责架构设计、技术选型、开发规划

避免冲突：
- Sequential Thinking不涉及具体的架构设计
- Software Planning Tool不进行深度的逻辑分析
- 按照"分析→设计→验证→规划"的顺序协作
```

### 场景4：三工具完整协作
```
完整工作流程：
1. Sequential Thinking：深度分析问题和需求
2. Software Planning Tool：基于分析制定技术架构
3. Claude Task Master：将架构转化为具体任务
4. Sequential Thinking：处理任务执行中的技术难点
5. Claude Task Master：跟踪进度和管理任务状态

三层分工：
- Sequential Thinking：思考层（分析、推理、解决难题）
- Software Planning Tool：设计层（架构、技术方案、规划）
- Claude Task Master：执行层（任务管理、进度跟踪、协调）

协调原则：
- 避免功能重叠，各司其职
- 按照复杂度递进的方式调用
- 保持信息流的连贯性
- 确保每个工具都能独立工作
```

## 智能工具选择策略

### 自动检测可用工具
```
检测逻辑：
1. 扫描当前环境中可用的MCP工具
2. 根据用户请求的类型和复杂度选择合适的工具组合
3. 优先使用功能最匹配的工具
4. 在多工具可用时，选择协作效果最佳的组合

工具优先级：
- 简单问题：直接解决，不调用专门工具
- 中等复杂度：选择单一最匹配的工具
- 高复杂度：使用多工具协作
```

### 渐进式工具调用
```
调用策略：
1. 评估问题复杂度和用户需求
2. 从最简单的解决方案开始
3. 根据需要逐步引入更多工具
4. 避免过度工程化

示例流程：
- 简单代码问题 → 直接解决
- 复杂逻辑问题 → Sequential Thinking
- 架构设计需求 → Software Planning Tool
- 大型项目管理 → 三工具协作
```

### 工具缺失时的降级策略
```
降级处理：
- 缺少Sequential Thinking：AI自行进行逻辑分析
- 缺少Software Planning Tool：AI自行进行架构设计
- 缺少Claude Task Master：AI自行进行任务分解

补偿机制：
- 提供更详细的分析和说明
- 增加验证和检查步骤
- 主动询问用户确认关键决策
- 建议用户安装缺失的工具以获得更好体验
```

## 协作质量保证

### 信息传递规范
```
工具间信息传递：
- 使用标准化的输出格式
- 包含足够的上下文信息
- 明确标注信息来源和可信度
- 保持信息的完整性和一致性

用户反馈集成：
- 每个阶段都要获取用户确认
- 及时调整工作方向
- 记录用户偏好和决策
- 在后续工作中应用学习到的偏好
```

### 错误处理和恢复
```
错误检测：
- 监控工具调用的成功率
- 检测输出质量和相关性
- 识别工具间的冲突和重复

恢复策略：
- 自动重试失败的工具调用
- 切换到备用工具或方法
- 降级到更简单的解决方案
- 向用户报告问题并请求指导
```

## 工具选择决策树

### 第一步：问题类型识别
```
用户请求分类：
├── 简单问题（单步解决）
│   └── 直接处理，不调用专门工具
├── 分析类问题（需要深度思考）
│   └── 检查Sequential Thinking可用性
├── 规划类问题（需要项目管理）
│   └── 检查Software Planning Tool可用性
├── 任务管理问题（需要跟踪执行）
│   └── 检查Claude Task Master可用性
└── 复合问题（需要多种能力）
    └── 检查多工具组合可用性
```

### 第二步：工具可用性检测
```
检测逻辑：
1. 扫描当前环境中的可用MCP工具
2. 记录每个工具的功能范围
3. 评估工具组合的协作效果
4. 选择最优的工具组合方案

可用性矩阵：
- 仅Sequential Thinking → 承担分析+设计+规划
- 仅Software Planning → 承担规划+分析+设计
- 仅Claude Task Master → 承担管理+分析+规划
- ST + SP → 分析+设计分工
- ST + CTM → 分析+管理分工
- SP + CTM → 设计+管理分工
- ST + SP + CTM → 三层分工协作
```

### 第三步：执行策略选择
```
策略决策：
1. 评估问题复杂度（简单/中等/复杂）
2. 匹配可用工具能力
3. 选择最适合的工具组合
4. 确定工具调用顺序
5. 设置协作边界和接口

执行原则：
- 优先使用最匹配的工具
- 避免功能重复和冲突
- 保持工作流程的连贯性
- 确保每个工具都能独立降级
```

## MCP Context7 + MCP DeepWiki 协同



---




## 1.工具选择规则

### 使用场景匹配

fetch_html:
- 需要分析网页结构和DOM元素时
- 需要提取HTML标签和属性时
- 网页内容包含复杂表格和格式化内容时

fetch_markdown:
- 需要更易读的格式化内容时
- 需要提取文章、博客、文档内容时
- 需要去除大部分HTML标签但保留基本格式时

fetch_txt:
- 只需要纯文本内容时
- 需要完全去除HTML标签和格式时
- 网页内容简单且主要是文字时

fetch_json:
- 目标URL返回JSON格式数据时
- 需要访问API端点时
- 需要结构化数据进行后续处理时



### 内容长度控制

max_length参数使用规则:
- 默认值: 5000字符
- 短内容提取: 1000-3000字符
- 标准内容提取: 5000-10000字符
- 长文档分析: 10000-20000字符
- 超长内容: 使用start_index参数分批获取

start_index参数使用规则:
- 首次调用: start_index=0
- 后续调用: start_index=上次内容长度
- 分页策略: 每次获取固定长度,直到获取完整内容



## 2.调用优化规则

### 性能优化



避免重复调用:
- 对相同URL的重复请求应合并
- 优先获取足够长度的内容
- 仅在必要时使用分批获取

缓存策略:
- 短时间内相同URL请求优先使用之前结果
- 动态内容(如新闻、股票)应每次重新获取
- 静态内容(如文档、教程)可重用之前结果

### 错误处理策略

常见错误类型:
- 网络连接问题: 等待后重试
- 404错误: 确认URL是否正确
- 403/401错误: 检查是否需要认证
- 内容解析错误: 尝试其他格式工具

重试策略:
- 网络错误: 最多重试2次
- 格式不匹配: 尝试其他fetch工具
- 内容过大: 使用分批获取策略



## 3.内容使用规则

### 信息提取优先级

提取内容类型优先级:
1. 用户明确询问的特定信息
2. 页面主要内容(正文、文档)
3. 相关元数据(标题、作者、日期)
4. 结构化数据(表格、列表)
5. 辅助内容(侧边栏、相关链接)

忽略内容类型:
- 广告和促销内容
- 导航菜单和页脚
- 评论区(除非用户明确需要)
- 无关的侧边栏内容



### 隐私和安全考量

敏感信息处理:
- 不提取个人识别信息(PII)
- 不存储或转发用户凭证
- 不访问需要登录的私有内容
- 不自动点击或提交表单

安全限制:
- 仅访问公开可访问的网页
- 不执行来自网页的脚本
- 不下载或执行二进制文件
- 遵守robots.txt规则



## 4.使用示例

### 标准调用示例

```python
# 获取网页HTML
fetch_html(url="https://example.com/article")

# 获取Markdown格式内容
fetch_markdown(url="https://example.com/blog-post")

# 获取纯文本内容
fetch_txt(url="https://example.com/simple-page")

# 获取JSON数据
fetch_json(url="https://api.example.com/data")
```



### 高级调用示例
```python
# 获取限定长度的内容
fetch_txt(
  url="https://example.com/long-article",
  max_length=10000
)

# 分批获取大型文档
# 第一批
first_batch = fetch_txt(
  url="https://example.com/documentation",
  max_length=8000,
  start_index=0
)
# 第二批
second_batch = fetch_txt(
  url="https://example.com/documentation",
  max_length=8000,
  start_index=8000
)

# 使用自定义headers
fetch_html(
  url="https://api.example.com/restricted-content",
  headers={
    "User-Agent": "Custom User Agent",
    "Authorization": "Bearer token123"
  }
)
```