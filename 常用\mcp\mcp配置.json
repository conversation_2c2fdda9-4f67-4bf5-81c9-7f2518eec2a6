{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "url": "http://localhost:3000/sse/mcp-feedback-enhanced"
    }
  }
}

{
  "mcpServers": {
    "context7": {
      "url": "http://localhost:3000/sse/context7"
    }
  }
}

{
  "mcpServers": {
    "basic-memory": {
      "url": "http://localhost:3000/sse/basic-memory"
    }
  }
}


{
  "mcpServers": {
    "shrimp-task-manager": {
        "command": "npx",
        "args": [
            "-y",
            "mcp-shrimp-task-manager"
        ],
        "env": {
            "DATA_DIR": "D:/software/14-ai/mcp/mcp-shrimp-task-manager/data_dir",
            "TEMPLATES_USE": "zh",
            "ENABLE_GUI": "false"
        }
    }
  }
}


docker run \
-p 3000:3000 \
-p 8765:8765 \
-v ./mcp_settings.json:/app/mcp_settings.json \
-v ./data:/app/data \
samanhappy/mcphub