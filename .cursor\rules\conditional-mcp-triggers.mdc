---
description: 基于条件的MCP触发规则
globs: ["**/*"]
alwaysApply: true
---

# 条件触发MCP规则

## 智能触发逻辑

### 1. 上下文感知触发
```
IF (用户提到项目名称 OR 当前目录包含项目文件) 
THEN 自动调用 ChromaDB 项目集合管理

IF (用户询问技术问题 AND 提到具体库名)
THEN 自动调用 resolve-library-id + get-library-docs

IF (用户描述开发任务 OR 提到"计划"、"实现")
THEN 自动调用 start_planning
```

### 2. 文件类型触发
```
IF (打开 .py, .js, .ts, .java 等代码文件)
THEN 自动调用 ChromaDB 搜索相关代码模式

IF (打开 package.json, requirements.txt, pom.xml)
THEN 自动调用 library documentation 工具获取依赖文档
```

### 3. 错误情况触发
```
IF (用户报告错误 OR 提到"bug"、"问题")
THEN 自动调用 ChromaDB 搜索历史解决方案

IF (编译/运行错误)
THEN 自动调用相关技术栈文档工具
```

### 4. 学习模式触发
```
IF (用户询问"如何"、"怎么做"、"最佳实践")
THEN 自动调用 ChromaDB 通用知识集合 + 相关文档

IF (用户分享学习心得 OR 解决方案)
THEN 自动调用 ChromaDB 保存到对应集合
```

## 优先级规则

1. **交互式反馈** - 最高优先级，每次交互都调用
2. **ChromaDB** - 高优先级，涉及知识管理时调用
3. **文档工具** - 中优先级，技术问题时调用
4. **规划工具** - 中优先级，任务管理时调用
5. **其他工具** - 按需调用