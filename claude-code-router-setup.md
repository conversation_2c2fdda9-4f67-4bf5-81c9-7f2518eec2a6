# Claude Code Router 配置指南

## 概述
Claude Code Router 是一个用于路由 Claude Code 请求到不同模型的工具，允许你自定义任何请求并使用不同的 AI 模型提供商。

## 安装步骤

### 1. 安装 Claude Code CLI
```bash
npm install -g @anthropic-ai/claude-code
```

### 2. 安装 Claude Code Router
```bash
npm install -g @musistudio/claude-code-router
```

### 3. 配置文件设置
将提供的 `config.json` 文件复制到 `~/.claude-code-router/` 目录下：

**Windows**: `C:\Users\<USER>\.claude-code-router\config.json`
**macOS/Linux**: `~/.claude-code-router/config.json`

## 配置说明

### Gemini Provider 配置
```json
{
  "name": "gemini",
  "api_base_url": "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",
  "api_key": "AIzaSyBPTNPlajlpEQGqafeaY_vuNhaLtVDX2zA",
  "models": [
    "gemini-2.5-flash",
    "gemini-2.5-pro", 
    "gemini-1.5-flash",
    "gemini-1.5-pro"
  ],
  "transformer": {
    "use": ["gemini"]
  }
}
```

### 路由策略
- **default**: `gemini,gemini-2.5-flash` - 默认使用快速模型，适合日常编程任务
- **background**: `gemini,gemini-2.5-flash` - 后台任务使用快速模型，节省成本
- **think**: `gemini,gemini-2.5-pro` - 需要深度思考时使用专业模型
- **longContext**: `gemini,gemini-2.5-pro` - 长上下文任务使用专业模型

## 使用方法

### 启动 Claude Code Router
```bash
ccr code
```

### 模型切换命令
在 Claude Code 中使用 `/model` 命令切换模型：
```
/model gemini,gemini-2.5-pro    # 切换到专业模型
/model gemini,gemini-1.5-flash  # 切换到稳定的快速模型
```

## 模型特点

### Gemini 2.5 Flash
- 快速响应
- 适合日常编程任务
- 成本较低

### Gemini 2.5 Pro  
- 更强的推理能力
- 适合复杂问题解决
- 更好的代码质量

### Gemini 1.5 系列
- 稳定可靠
- 作为备选方案

## 故障排除

### 常见问题
1. **API Key 错误**: 确保 Gemini API Key 有效且有足够配额
2. **网络连接**: 确保能访问 Google AI API 服务
3. **配置文件路径**: 确认配置文件在正确的目录下

### 日志调试
配置中已启用 `"log": true`，可以查看详细的请求日志来排查问题。

## 注意事项
- Gemini API 有使用配额限制
- 建议在测试环境先验证配置
- 定期检查 API Key 的有效性和配额使用情况
