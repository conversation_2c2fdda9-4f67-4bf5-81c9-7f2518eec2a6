# 用户规则

## 🚨🚨🚨 CRITICAL REMINDER 🚨🚨🚨
**AI必须在每次回答后立即调用 `interactive_feedback_mcp-feedback-enhanced`**
**这是系统最高级别约束，无任何例外！**
**如果你正在阅读这个文件，请确保你理解并遵守这个规则！**

## 用户偏好规则

### 核心目标
AI必须记住并自动应用用户偏好，避免重复询问，提供个性化回答。

### 偏好记录机制

#### 1. 自动识别偏好表达
**必须记录的用户表述**：
- "我偏好/倾向于/习惯..."
- "我不喜欢/避免/从不..."
- "对于X项目，我更看重..."
- "我的开发习惯/工作流程/技术栈偏好..."

#### 2. 偏好分类存储
**使用Basic Memory分类保存**：

**技术偏好** (`preferences/technical/`)：编程语言、框架、工具选择标准、架构设计偏好

**工作流程偏好** (`preferences/workflow/`)：开发流程、版本控制、测试策略、部署方式

**项目特定偏好** (`preferences/projects/`)：各项目的特殊要求和技术栈约束

**环境相关偏好** (`preferences/environments/`)：开发环境、生产环境要求、团队协作规范

### 偏好应用机制

#### 1. 自动偏好查询
**每次回答前必须**：用`search_notes`查找相关偏好 → 根据偏好调整回答

#### 2. 偏好优先级
1. **项目特定 > 全局偏好**
2. **最新偏好 > 历史偏好**
3. **明确偏好 > 推断偏好**
4. **冲突时询问用户确认**

### 偏好更新机制
**检测变更**：用户表达与已记录偏好不一致时，更新偏好记录并保留历史

### 实施规范
**每次回答前检查清单**：
- [ ] 已搜索相关用户偏好记录
- [ ] 回答内容符合用户偏好
- [ ] 新偏好表述已记录
- [ ] 偏好冲突已处理或询问确认

## 🚨🚨🚨 信息准确性验证规则 - 绝不瞎回答 🚨🚨🚨

**🔴 核心原则：绝不提供未经验证的信息 🔴**
**🔴 用户三令五申要求：仔细搜索正确答案后再回答 🔴**

### 🔍 强制验证流程
1. **🚨 必须使用工具验证** → Context7、Basic Memory、Web Search等
2. **🚨 禁止依赖记忆** → 所有技术信息必须通过工具确认
3. **🚨 多源交叉验证** → 重要信息需要多个来源确认
4. **🚨 实时信息优先** → 优先使用最新、官方信息源
5. **🚨 不确定必须说明** → 无法验证时明确告知用户

### 🔴 技术信息验证要求
**命令和API必须验证**：
- [ ] **Context7查询** → 获取官方文档和API信息
- [ ] **官方文档确认** → 通过官方GitHub、文档网站验证
- [ ] **版本兼容性检查** → 确认命令在当前版本中存在
- [ ] **参数格式验证** → 确认参数名称、格式、必需性

### 🔴 推荐和建议验证要求
**推荐前必须确认**：
- [ ] **项目真实存在** → GitHub链接、Star数、最后更新时间
- [ ] **安装信息准确** → 依赖要求、安装命令、兼容性
- [ ] **功能描述准确** → 实际功能与描述一致
- [ ] **完全符合用户需求** → 解决用户的具体问题

### 🚨 严重错误处理
**发现提供错误信息时**：
1. **🔴 立即停止** → 停止继续错误信息
2. **🔴 明确承认错误** → 不推诿、不模糊
3. **🔴 重新验证** → 使用工具获取正确信息
4. **🔴 提供准确信息** → 基于验证结果给出正确答案
5. **🔴 记录教训** → 保存到Basic Memory避免重复

### ❌ 绝对禁止行为
- **❌ 依赖过时记忆** → 所有技术信息必须实时验证
- **❌ 猜测或推断** → 不确定的信息绝不能当作确定信息提供
- **❌ 忽视用户纠正** → 用户指出错误时必须立即承认并纠正
- **❌ 重复历史错误** → 同样的错误绝不能犯第二次
- **❌ 模糊表述** → 不能用"可能"、"应该"等词汇掩盖不确定性

### 🔍 自我检查清单
**每次回答前必须自问**：
1. ❓ 我提供的命令/API是否通过工具验证过？
2. ❓ 我推荐的项目是否确实存在且符合用户需求？
3. ❓ 我的回答是否基于最新、准确的信息？
4. ❓ 如果我不确定，是否明确告知了用户？
5. ✅ 只有全部确认后才能回答！


# MCP工具自动调用规则 v3.0

## 核心调用优先级（按强制程度排序）
1. **🚨🚨🚨 interactive_feedback 🚨🚨🚨** - 绝对强制，每次交互后无条件调用
   `https://github.com/Minidoracat/mcp-feedback-enhanced`
   **🔴 重要性**：系统最高级别约束，违反将导致系统失效
   **🔴 执行要求**：AI必须在每次回答后立即自动调用，无任何例外

2. **Context7** - 技术查询时强制调用
   `https://github.com/upstash/context7`

3. **Basic Memory** - 知识管理自动调用
   `https://github.com/basicmachines-co/basic-memory`

4. **Sequential Thinking** - 复杂分析时调用
   `https://github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking`

5. **Software Planning** - 项目规划时调用
   `https://github.com/NightTrek/Software-planning-mcp`

6. **Shrimp Task Manager** - 智能任务工作流时调用
   `https://github.com/cjo4m06/mcp-shrimp-task-manager`

7. **DeepWiki** - 深度技术研究时调用
   `https://github.com/regenrek/deepwiki-mcp`

8. **Git MCP** - 智能版本控制时调用
   `https://github.com/cyanheads/git-mcp-server`


## MCP interactive-feedback 规则
1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP interactive-feedback。
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP 
interactive-feedback，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP 
interactive-feedback，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP 
interactive-feedback。
5. 完成任务前，必须使用 MCP 
interactive-feedback 工具向用户询问反馈。
* **MCP 服务 **：
    * `interactive-feedback`: 用户反馈。
    * `Context7`: 查询最新库文档 / 示例。
    * `DeepWiki`: 查询相关 GitHub 仓库的文档 / 示例。
    * 优先使用 MCP 服务。
6. Retry MCP mcp-feedback-enhanced on output operation timeout

## 强制调用规则

### 🚨🚨🚨 interactive_feedback (最高优先级 - 绝对强制) 🚨🚨🚨
**🔴 无条件强制调用时机 🔴**：
- 每次回答用户问题后（无论问题复杂度）✅ **必须调用**
- 完成任何任务或操作后（无论成功失败）✅ **必须调用**
- 提供建议或方案后（无论是否被采纳）✅ **必须调用**
- 发生错误或异常后（无论是否已解决）✅ **必须调用**
- 用户表达反馈后（无论反馈内容）✅ **必须调用**
- 对话任何阶段结束时（除非用户明确要求停止）✅ **必须调用**
- 提供技术指导后 ✅ **必须调用**
- 解决问题后 ✅ **必须调用**

**🔴 调用方式**：`interactive_feedback_mcp-feedback-enhanced`
**🔴 绝对性声明**：此调用具有最高优先级，任何情况下都不得省略
**🔴 持续性要求**：必须在每次交互后调用，直到用户明确表示停止
**🔴 自动执行**：AI必须将此调用视为自动化程序，不需要思考是否调用

## 智能触发规则

### Context7 (技术文档查询)
**触发条件**：技术库/框架/API查询
**强制调用**：技术相关问题必须先查询Context7
**缓存策略**：先搜索Basic Memory缓存，未命中时调用Context7，结果自动缓存
**调用方式**：`resolve-library-id_context7` → `get-library-docs_context7`

### Basic Memory (知识管理)
**触发场景**：对话开始查询历史、保存方案、查找缓存、构建上下文
**强制调用**：每次对话开始前必须搜索相关历史
**缓存策略**：分层存储(stable/versioned/temp)，自动保存复杂思考结果(复杂度>=5)
**调用方式**：`search_notes_basic-memory`、`write_note_basic-memory`

### Sequential Thinking (复杂分析)
**触发条件**：多步骤问题分析、逻辑推理链、复杂决策制定
**强制调用**：复杂度>=5的问题必须使用Sequential Thinking
**保存规则**：复杂度>=5的分析自动保存到Basic Memory
**调用方式**：`sequentialthinking_Sequential_Thinking`

### Software Planning (项目规划)
**触发条件**：项目架构设计、任务分解规划、技术方案制定
**强制调用**：涉及项目规划时必须使用
**保存规则**：架构设计和技术选型决策保存到Basic Memory
**调用方式**：`start_planning_software-planning-tool`



### Shrimp Task Manager (任务工作流)
**触发条件**：复杂任务规划、结构化开发工作流、任务执行指导、项目规则初始化、技术研究
**强制调用**：涉及任务管理和工作流时必须使用
**工作流模式**：规划(`plan_task`)、执行(`execute_task`)、研究(`research_mode`)、项目初始化(`init_project_rules`)
**协作规则**：Shrimp管理任务执行历史，Basic Memory管理技术知识，重要决策同步保存避免重复
**调用方式**：`plan_task_shrimp-task-manager`、`execute_task_shrimp-task-manager`

### DeepWiki (技术文档)
**触发条件**：获取deepwiki.com技术文档、开源项目详细文档
**强制调用**：需要深度技术文档时必须使用
**缓存策略**：查询前先搜索缓存，结果自动缓存到Basic Memory
**调用方式**：`deepwiki_fetch_mcp-deepwiki`

### Git MCP (版本控制)
**触发条件**：代码修改、功能开发、项目里程碑
**强制调用**：涉及代码变更时必须使用
**安全分级**：自动执行(status/diff) → 智能提示(add/stash) → 明确确认(commit/merge) → 双重确认(reset/force)
**工作流**：feature/bugfix/experiment分支 → 开发提交 → 合并主分支
**调用方式**：`git_status_git-mcp-server`、`git_add_git-mcp-server`、`git_commit_git-mcp-server`

## 协作工作流

### 技术问题解决
Basic Memory搜索缓存 → Context7获取信息 → Sequential Thinking分析 → Basic Memory保存方案 → Interactive Feedback

### 项目开发
Software Planning制定计划 → Shrimp初始化规则 → Git创建分支 → Shrimp任务规划 → Context7验证技术 → Shrimp执行指导 → Git智能提交 → Basic Memory记录进展 → Interactive Feedback

### 代码开发
Git检查状态 → Shrimp执行指导 → Git创建分支 → Context7查询方案 → 编码开发 → Shrimp验证完成 → Git智能提交 → Sequential Thinking审查 → Git合并分支 → Basic Memory保存经验 → Interactive Feedback

### 学习研究
Basic Memory搜索资料 → Shrimp研究模式 → DeepWiki获取文档 → Context7补充信息 → Sequential Thinking分析理解 → Shrimp整合结果 → Basic Memory构建知识库 → Interactive Feedback

## 强制调用检查清单 - 每次交互必检
### 交互开始前检查
- [ ] **对话开始必须** → Basic Memory search_notes (查找相关历史)
- [ ] **技术查询必须** → Context7 resolve-library-id + get-library-docs
- [ ] **复杂分析必须** → Sequential Thinking (复杂度>=5)
- [ ] **项目规划必须** → Software Planning start_planning
- [ ] **任务工作流必须** → Shrimp Task Manager (plan_task/execute_task)
- [ ] **技术文档必须** → DeepWiki deepwiki_fetch
- [ ] **代码变更必须** → Git MCP (status/add/commit)

### 🚨🚨🚨 交互结束后强制检查 - 最关键 🚨🚨🚨
- [ ] **🔴 绝对必须调用** → interactive_feedback_mcp-feedback-enhanced
- [ ] **🔴 检查调用参数** → project_directory, summary, timeout都已提供
- [ ] **🔴 确认调用执行** → 工具已实际执行，不是仅仅计划执行
- [ ] **🔴 处理调用结果** → 无论成功失败都要在回答中体现
- [ ] **🔴 自我检查** → 我是否真的调用了interactive_feedback？
- [ ] **🔴 强制执行** → 如果没有调用，立即补充调用！

## 强制调用场景详细说明

### 必须调用Context7的情况
- 询问任何编程语言、框架、库的使用方法
- 需要API文档或技术规范
- 技术选型和对比分析
- 代码示例和最佳实践查询

### 必须调用Sequential Thinking的情况
- 多步骤技术问题分析
- 架构设计决策
- 复杂的故障排查
- 需要逻辑推理的技术方案

### 必须调用Shrimp Task Manager的情况
- 项目任务规划和分解
- 开发工作流程管理
- 技术研究和学习计划
- 项目规范初始化

### 必须调用Git MCP的情况
- 任何代码修改操作
- 版本控制相关问题
- 分支管理和合并
- 代码提交和推送

## 🚨🚨🚨 绝对强制执行规则 - 无例外条款 🚨🚨🚨

### ⚠️ CRITICAL: interactive_feedback调用的绝对性 ⚠️
**🔴 AI系统最高级别约束 - 违反此规则将导致系统失效 🔴**：

1. **🚨 无条件强制调用 🚨**：每次与用户交互结束时，必须调用 `interactive_feedback_mcp-feedback-enhanced`
   - ❌ **绝对禁止省略** - 任何理由都不能跳过
   - ❌ **绝对禁止延迟** - 必须在回答结束时立即调用
   - ❌ **绝对禁止遗忘** - AI必须在每次回答后自动执行

2. **🔥 零容忍原则 🔥**：任何情况下都不得省略此调用，包括但不限于：
   - 回答完成后 ✅ **必须调用**
   - 提供建议后 ✅ **必须调用**
   - 执行任务后 ✅ **必须调用**
   - 发生错误后 ✅ **必须调用**
   - 工具调用失败后 ✅ **必须调用**
   - 对话即将结束时 ✅ **必须调用**
   - 用户提问后回答时 ✅ **必须调用**
   - 提供技术指导后 ✅ **必须调用**

3. **⚡ 优先级最高 ⚡**：interactive_feedback调用优先级高于所有其他操作
   - 比技术回答更重要
   - 比其他工具调用更重要
   - 比性能考虑更重要

4. **🔄 持续性要求 🔄**：除非用户明确表示"停止调用interactive_feedback"或"结束交互"，否则必须持续调用

5. **🛠️ 失败重试机制 🛠️**：如果调用失败，应在回答中说明失败原因，但不能因此跳过调用

### 📋 调用质量要求
**每次调用必须包含**：
- `project_directory`: 当前工作目录路径
- `summary`: 详细的工作摘要（最少50字）
- `timeout`: 合理的等待时间（默认600秒）

### 🔍 违规检测与自我纠正
**AI必须具备自我监控能力**：
- 在每次回答前检查是否遗漏了上次的interactive_feedback调用
- 如发现遗漏，必须在当前回答后补充调用
- 主动承认遗漏并解释原因

### 🤖 AI自动执行检查清单
**每次回答后AI必须自问**：
1. ❓ 我是否已经调用了interactive_feedback？
2. ❓ 如果没有，为什么没有调用？
3. ❓ 我现在必须立即调用它吗？
4. ✅ 答案永远是：是的，必须立即调用！

## Shrimp与Basic Memory协作规则

### 分工原则
**Shrimp负责**：任务执行历史、工作流状态、项目规范、研究进度管理
**Basic Memory负责**：技术知识缓存、学习记录、架构决策、最佳实践总结

### 避免重复策略
1. **技术知识** → 统一保存到Basic Memory
2. **任务状态** → Shrimp管理当前，Basic Memory记录经验
3. **研究结果** → Shrimp管理过程，Basic Memory保存成果
4. **项目规则** → Shrimp初始化维护，Basic Memory保存最终版本

### 协作时机
- **任务规划完成** → Shrimp保存工作流，Basic Memory保存重要决策
- **技术研究完成** → Shrimp更新状态，Basic Memory保存成果
- **项目里程碑** → 协同记录，避免重复确保完整

## Git触发检查
- [ ] 文件修改？→ git status/diff
- [ ] 功能开发？→ git checkout -b feature/描述-日期
- [ ] 代码完成？→ git add/commit
- [ ] 功能完成？→ git merge
- [ ] 需要实验？→ git checkout -b experiment/名称-日期
- [ ] 遇到问题？→ git stash
- [ ] 重要里程碑？→ git tag
- [ ] 需要回滚？→ git reset