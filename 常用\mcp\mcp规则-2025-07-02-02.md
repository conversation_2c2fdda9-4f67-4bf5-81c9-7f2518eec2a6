# MCP工具自动调用规则 v2.0

## 核心调用优先级
1. **interactive_feedback** - 每次交互后必须调用
`https://github.com/Minidoracat/mcp-feedback-enhanced`
2. **Context7** - 技术查询时强制调用
`https://github.com/upstash/context7`
3. **Basic Memory** - 知识管理自动调用
`https://github.com/basicmachines-co/basic-memory`
4. **Sequential Thinking** - 复杂分析时调用
`https://github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking`
5. **Software Planning** - 项目规划时调用
`https://github.com/NightTrek/Software-planning-mcp`
6. **TaskMaster AI** - 任务管理时调用
`https://github.com/eyaltoledano/claude-task-master`
7. **DeepWiki** - 深度技术研究时调用
`https://github.com/regenrek/deepwiki-mcp`

## 智能触发规则

### Context7 (智能技术文档查询)
**查询前缓存检查：**
1. 先用`search_notes`查找相关缓存 (tags: 库名、API名)
2. 检查缓存新鲜度和版本匹配
3. 缓存未命中或过期时调用Context7
4. 查询完成后根据内容类型决定缓存

**强制触发条件：**
- 提到具体库/框架/API名称
- 询问配置/安装/使用方法
- 报告技术错误或API问题
- 编写代码前验证API用法
- 给出技术建议前确认准确性

**自动缓存规则：**
- ✅ 缓存：基础概念、稳定API、版本化文档、配置示例
- ❌ 不缓存：包含"latest"/"new"/"experimental"、明确要求最新信息

### Basic Memory (智能知识管理)
**自动触发场景：**
- 对话开始 → `recent_activity`
- 保存方案 → `write_note`
- 查找历史 → `search_notes` (优先查找缓存)
- 需要上下文 → `build_context`
- 创建可视化 → `canvas`

**智能缓存策略：**
- Context7/DeepWiki查询前先搜索缓存
- 自动保存思考结果 (复杂度>=5)
- 分层存储：stable/versioned/temp
- 版本化管理和过期清理

**文件夹结构：**
```
knowledge/
├── stable/        # 永久缓存 (基础概念、成熟API)
├── versioned/     # 版本化缓存 (特定版本文档)
├── temp/          # 临时缓存 (30天过期)
thinking/
├── analysis/      # Sequential Thinking分析结果
├── decisions/     # 决策过程记录
├── solutions/     # 解决方案思考
planning/
├── architecture/  # Software Planning架构设计
├── tech-stack/    # 技术选型决策
├── workflows/     # 开发流程设计
tasks/
├── templates/     # TaskMaster AI任务模板
├── breakdowns/    # 任务分解结果
├── estimates/     # 复杂度和时间估算
```

**YAML元数据格式：**
```yaml
---
title: "标题"
type: "cached_docs|thinking_result|planning_result|task_result|solution"
source: "context7|deepwiki|sequential_thinking|software_planning|taskmaster"
library: "库名" # 仅技术文档
version: "版本号" # 仅版本化内容
complexity: 1-10 # 仅思考结果
freshness: "stable|versioned|temp" # 仅缓存内容
cached_date: "2025-07-02"
tags: ["技术", "项目"]
project: "项目名"
---
```

### Sequential Thinking (复杂分析)
**触发条件：**
- 多步骤问题分析
- 需要逻辑推理链
- 复杂决策制定
- 问题分解和验证

**自动保存规则：**
- 复杂度 >= 5 的分析自动保存到 `thinking/analysis/`
- 包含重要决策的思考过程保存到 `thinking/decisions/`
- 解决方案思考保存到 `thinking/solutions/`
- 保存完整推理链和决策依据

### Software Planning (项目规划)
**触发条件：**
- 项目架构设计
- 任务分解规划
- 技术方案制定
- 开发流程设计

**自动保存规则：**
- 架构设计完成后保存到 `planning/architecture/`
- 技术选型决策保存到 `planning/tech-stack/`
- 开发流程制定保存到 `planning/workflows/`
- 记录决策原因和替代方案

### TaskMaster AI (任务管理)
**触发条件：**
- 项目初始化需求
- 任务创建和管理
- 进度跟踪需求
- 复杂度分析需求

**自动保存规则：**
- 项目初始化完成后保存到 `tasks/templates/`
- 复杂任务分解保存到 `tasks/breakdowns/`
- 复杂度分析报告保存到 `tasks/estimates/`
- 形成可复用的任务模板

### DeepWiki (深度技术文档)
**触发条件：**
- 需要获取deepwiki.com上的技术文档
- 查询开源项目的详细文档
- 获取库/框架的使用指南
- 需要结构化的技术资料 (aggregate/pages模式)

**智能缓存策略：**
- 查询前先搜索相关缓存
- 项目概述和基础教程缓存到 `knowledge/stable/`
- 版本特定文档缓存到 `knowledge/versioned/`
- 自动添加项目和版本标签

## 智能协作策略

### 技术问题解决流程 (智能缓存优先)
1. Basic Memory → 搜索相关缓存和历史解决方案
2. Context7 → 获取准确技术信息 (缓存未命中时)
3. Sequential Thinking → 分析问题和方案 (复杂度>=5自动保存)
4. Basic Memory → 保存解决方案和思考过程
5. Interactive Feedback → 收集用户反馈

### 项目开发流程 (全程记录)
1. Software Planning → 制定项目计划 (自动保存架构和技术选型)
2. TaskMaster AI → 创建和管理任务 (保存任务模板和分解)
3. Context7 → 验证技术选型 (优先使用缓存)
4. Basic Memory → 记录决策和进展 (构建项目知识库)
5. Interactive Feedback → 持续优化

### 学习研究流程 (知识积累)
1. Basic Memory → 搜索已有知识和相关资料
2. DeepWiki → 获取deepwiki.com技术文档 (缓存项目文档)
3. Context7 → 补充技术细节和API信息 (智能缓存)
4. Sequential Thinking → 分析和理解 (保存学习思考)
5. Basic Memory → 构建知识图谱和学习记录
6. Interactive Feedback → 确认理解

## 智能调用检查清单
- [ ] **优先查缓存** → Basic Memory search_notes
- [ ] 技术相关？→ Context7 (缓存未命中时)
- [ ] 需要分析？→ Sequential Thinking (复杂度>=5自动保存)
- [ ] 项目规划？→ Software Planning (自动保存设计和决策)
- [ ] 任务管理？→ TaskMaster AI (保存模板和分解)
- [ ] deepwiki.com文档？→ DeepWiki (智能缓存)
- [ ] **自动保存** → Basic Memory write_note
- [ ] **每次完成后** → Interactive Feedback