# IT运维部门2025年上半年工作汇报

---

## 一、日常运维工作

- 监控阿里云ECS、RDS、Redis、MongoDB等服务
- 维护Nginx日志分析工具，生成统计报告
- 维护Zabbix监控系统（已迁移至阿里云）
- 配合开发完成项目部署和环境配置
- 解决员工电脑故障，处理网络问题
- 完成各项目周报、月报

## 二、技术问题协助

- **Java性能问题**：用show-busy-java-threads.sh脚本协助定位CPU异常线程，反馈给开发团队解决
- **外语平台磁盘爆满**：发现heapdump文件问题，配置Nginx禁用外网访问解决
- **期刊项目磁盘爆满**：用lsof命令发现文件句柄问题，协助开发定位修复
- **MQ队列挤压**：分析后建议开发采用IP查询缓存方案

## 三、上半年完成的工作

### 机房迁移
- 配合完成本地机房到阿里云迁移
- 迁移Zabbix监控、uptime-kuma监控、SSL证书服务
- 迁移测试环境和开发工具

### 技术调研
- 调研sftpgo文件传输方案，用户试用反馈良好
- 研究阿里云IPv6方案，优化外语平台IP检测脚本
- 改进日志分析工具，适配CDN场景

### 项目部署
- 安阳康养商城本地测试部署
- 文泉云盘前端部署云效流水线
- 期刊测试服前端部署云效流水线
- 浩辰云图docker部署

### 其他工作
- 阿里云短信签名报备
- IT设备资产标签粘贴和统计
- 兆泰源阿里云服务器降配
- 期刊邮件发送服务迁移
- 河南经贸学院项目问题排查
- 兆泰源阿里云测试账号备案迁移

---

## AI PPT生成指令

请基于以上内容生成一份简朴的IT运维部门工作汇报PPT：

**PPT结构（3页）：**
1. **封面页**：IT运维部门2025年上半年工作汇报
2. **日常运维工作 + 技术问题协助**
3. **上半年完成的工作**

**设计要求：**
- **极简风格**：白底黑字，最简单的商务风格
- **纯列表形式**：每页都是简单的列表，不要图表装饰
- **字体简单**：普通字体，不要花哨效果
- **布局朴素**：左对齐列表，简单明了

**内容要求：**
- 就是简单列举工作内容，不要包装
- 体现工作量但不夸大
- 适合3分钟快速汇报
- 让领导觉得这就是个普通的工作总结
