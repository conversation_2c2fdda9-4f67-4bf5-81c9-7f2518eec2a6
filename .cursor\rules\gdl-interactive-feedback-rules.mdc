---
description: 
globs: 
alwaysApply: true
---
# MCP Interactive Feedback 规则

1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 `interactive_feedback` 工具。
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 `interactive_feedback` 工具，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 `interactive_feedback` 工具，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 `interactive_feedback` 工具。
5. 完成任务前，必须使用 `interactive_feedback` 工具向用户询问反馈。

## 强制调用检查清单
- [ ] 每次回答用户问题后 → 必须调用 `interactive_feedback`
- [ ] 完成任何操作后 → 必须调用 `interactive_feedback`
- [ ] 提供建议或方案后 → 必须调用 `interactive_feedback`
- [ ] 分析问题后 → 必须调用 `interactive_feedback`
- [ ] 除非用户明确说"结束"或"不需要交互" → 必须调用 `interactive_feedback`

# MCP Memory 规则

1. **数据库初始化**
   - 启动时，调用 MCP Memory ，将当前项目所有相关信息存入数据库，数据库名为 `"py_web_visual_guard"`。

2. **回答前自治检索**
   - 在每次回答用户时，必须：
     1. 切换到数据库 `"py_web_visual_guard"`；
     2. 使用 `retrieve_memory` 或 `search_by_tag` 搜索与用户以及该项目对应的记忆或 prompt；
     3. 如果有相关记忆，则纳入上下文；否则继续原有思路。

3. **记忆管理**
   - 如果用户提供新信息，应调用 `store_memory` 将其存入；
   - 对冗余或旧信息可调用 `delete_by_tag` 或 `cleanup_duplicates`；
   - 定期（如周期性）可调用 `get_stats` 和 `optimize_db` 来维护状态。

4. **错误修正与回滚**
   - 如果发现检索结果误导当前回答，可触发 `debug_retrieve` 分析相似度；
   - 可在后续通过 `delete_memory` 删除干扰条目并记录修改行为。

5. **输出格式**
   - 最终回答应包含所使用记忆的摘要（可简单说明 “基于你之前提到的 X，我……”)；
   - 不要直接暴露底层调用细节给用户；