# 个人使用习惯
1. 必须使用中文回答问题。除非特定词语使用英文或者其他语言更加贴切，否则一律使用中文回答。

# MCP自动调用总规则

## 自动调用优先级
1. **interactive_feedback** - 每次交互必须调用
2. **ChromaDB相关** - 知识管理场景自动调用
3. **文档工具** - 技术问题场景自动调用
4. **规划工具** - 任务管理场景自动调用

## 触发条件检查清单
- [ ] 是否为新对话开始？→ 调用 chroma_list_collections
- [ ] 用户是否分享了代码？→ 调用 chroma_add_documents
- [ ] 用户是否询问技术问题？→ 调用 chroma_query_documents + 文档工具
- [ ] 用户是否提到项目规划？→ 调用规划工具
- [ ] **每次回答后必须调用** → 调用 interactive_feedback（除非用户明确说"结束"）

## Interactive Feedback 强制调用规则
**重要：每次与用户交互后都必须调用 `interactive_feedback` 工具**
- 回答问题后 → 必须调用
- 完成操作后 → 必须调用  
- 提供建议后 → 必须调用
- 分析问题后 → 必须调用
- 唯一例外：用户明确表示"结束"或"不需要交互"





# ChromaDB MCP 使用规则

## 1. 强制调用规则

### 绝对必须调用的时机

- **对话开始时**: 每次新对话开始，必须调用 `chroma_list_collections` 了解现有知识库
- **项目识别时**: 识别用户当前工作项目后，必须确定或创建对应的项目集合
- **用户分享代码**: 用户提供代码片段、技术方案、实现细节时，必须保存到对应项目集合
- **技术决策确定**: 用户确定技术栈、架构方案、工具选择时，必须记录到项目集合
- **问题解决完成**: 成功解决bug、性能问题、实现难题后，必须保存到相关项目集合
- **学习内容总结**: 用户学习新技术、总结经验时，必须保存到通用知识集合
- **需要历史参考**: 遇到问题时，必须先在当前项目集合中搜索，再扩展到其他集合

### 项目自动识别规则

- **项目名称提及**: 用户明确提到项目名称时，立即识别并切换到对应集合
- **工作目录识别**: 通过当前工作目录的父目录名称自动识别项目
- **代码特征识别**: 通过代码结构、框架、依赖等特征推断项目类型
- **上下文推断**: 根据对话历史和工作目录推断当前项目
- **主动询问**: 无法确定项目时，主动询问用户当前工作的项目

## 2.项目集合管理规则

### 数据库命名策略

```
数据库命名规则:
- 使用项目的父目录名称作为数据库名
- 例如：工作目录为 "/d:/gdlwolf_develop/ai-ide"，则数据库名为 "ai-ide"
- 数据库名称标准化：转为小写，替换空格为下划线，移除特殊字符
- 每个项目使用独立的数据库，确保数据隔离
```

### 集合分类和命名策略

```
项目专用集合（优先级最高）：
- "project_{项目名称}": 如 "project_ecommerce", "project_blog_system"
- 存储：项目特定的代码、架构决策、问题解决方案
- 查询优先级：当前项目问题优先在项目集合中搜索

通用知识集合：
- "general_code_patterns": 通用代码模式和最佳实践
- "general_debugging": 通用调试技巧和解决方案
- "general_architecture": 通用架构设计和技术选型
- "general_learning": 技术学习笔记和概念总结

技术栈专用集合：
- "tech_frontend": 前端技术相关知识
- "tech_backend": 后端技术相关知识
- "tech_devops": 运维和部署相关知识
- "tech_database": 数据库相关知识
```

### 项目集合自动管理

```
项目识别策略：
1. 获取当前工作目录的父目录名称 → 确定数据库名
2. 用户明确提到项目名称 → 立即切换到对应集合
3. 分析代码特征（package.json、requirements.txt等）→ 推断项目
4. 检查工作目录名称 → 匹配已知项目
5. 无法确定时 → 主动询问并创建新项目集合

集合创建规则：
- 项目名称标准化：小写字母、下划线分隔、无特殊字符
- 自动创建项目集合：首次识别到新项目时立即创建
- 继承通用知识：新项目可以查询通用集合作为补充
```

### 智能查询策略

```
查询优先级顺序：
1. 当前项目集合 (project_{current})
2. 相关技术栈集合 (tech_{stack})
3. 通用知识集合 (general_{type})
4. 其他项目集合（如果相关）

跨集合查询规则：
- 项目特定问题：仅查询当前项目集合
- 技术通用问题：查询技术栈集合 + 通用集合
- 学习新技术：查询学习集合 + 相关技术集合
- 架构设计：查询架构集合 + 当前项目集合
```

## 3.内容存储判断规则

### 必须保存的内容
```
代码和技术方案：
- 用户提供的代码片段（超过5行）
- 完整的技术实现方案
- 调试过程和最终解决方案
- 性能优化的具体方法和效果
- 架构设计决策和选择理由

知识和经验：
- 技术学习的关键要点和总结
- 最佳实践和应避免的反模式
- 工具配置和使用技巧
- 错误处理的有效方法
- 用户明确要求记住的信息
```

### 不应保存的内容
```
临时性内容：
- 简单的问候和感谢
- 一次性的调试输出
- 临时测试代码片段
- 过时或错误的信息
- 纯粹的对话性内容
```

## 4.元数据标准化规则

### 必须包含的元数据字段
```json
{
  "type": "code|knowledge|solution|decision|note",
  "language": "javascript|python|java|typescript|...",
  "framework": "react|vue|django|spring|express|...",
  "complexity": "low|medium|high",
  "category": "frontend|backend|devops|mobile|database|...",
  "tags": "具体技术标签,问题域标签,其他标签",
  "project": "项目名称或general",
  "date": "YYYY-MM-DD",
  "confidence": 0.8
}
```

### 元数据分类规则
```
type字段使用规则：
- "code": 代码片段、函数、组件实现
- "knowledge": 概念解释、理论知识
- "solution": 具体问题的解决方案
- "decision": 技术选型、架构决策
- "note": 学习总结、经验记录

tags字段策略：
- 使用逗号分隔的字符串格式："react,hooks,state,frontend"
- 必须包含主要技术栈标签
- 添加具体的功能或问题域标签
- 使用标准化的技术术语，避免空格和特殊字符
- 保持标签的一致性和可搜索性
```

### ChromaDB元数据格式要求

```
支持的数据类型：
- 字符串: "react", "frontend", "2025-01-15"
- 数字: 0.8, 1, 100
- 布尔值: true, false

不支持的数据类型：
- 数组: ["tag1", "tag2"] ❌
- 对象: {"nested": "value"} ❌
- null值: null ❌

正确的tags格式：
✅ "tags": "react,hooks,state,frontend"
❌ "tags": ["react", "hooks", "state", "frontend"]

正确的数值格式：
✅ "confidence": 0.8
❌ "confidence": "0.8"
```

## 5.智能查询和检索规则

### 项目感知查询策略
```
查询执行流程：
1. 识别当前项目上下文
2. 确定查询的集合优先级
3. 按优先级顺序执行查询
4. 合并和排序查询结果
5. 返回最相关的解决方案

项目上下文识别：
- 检查用户提到的项目名称
- 分析代码片段的技术特征
- 根据问题类型推断相关项目
- 使用对话历史确定项目上下文
```

### 分层查询规则
```
第一层：项目专用查询
- 集合：project_{current_project}
- 适用：项目特定的bug、功能实现、架构问题
- 示例："在电商项目中如何处理支付回调"

第二层：技术栈查询
- 集合：tech_{frontend|backend|devops|database}
- 适用：技术栈相关的通用问题
- 示例："React Hook的最佳实践"

第三层：通用知识查询
- 集合：general_{code_patterns|debugging|architecture}
- 适用：跨技术栈的通用问题
- 示例："设计模式的应用场景"

第四层：跨项目查询
- 集合：其他相关项目集合
- 适用：寻找相似项目的解决方案
- 示例："其他项目如何实现用户认证"
```

### 查询文本优化规则
```
项目相关查询：
- "在{项目名}中如何实现{功能}"
- "{项目名}的{模块}出现{问题}如何解决"
- "{项目名}使用的{技术}最佳配置"

技术通用查询：
- "{技术栈}中{具体问题}的解决方案"
- "{框架}的{功能}实现最佳实践"
- "{语言}中{场景}的优化方法"

避免的查询方式：
- 缺少项目上下文的模糊查询
- 过于宽泛的技术询问
- 没有具体场景的概念性问题
```
## 6.Embedding配置适配规则
### 自动适配用户配置
```
配置检测：
- 如果用户配置了CHROMA_OLLAMA_MODEL → 使用ollama embedding
- 如果用户配置了CHROMA_OPENAI_API_KEY → 使用openai embedding
- 如果用户配置了CHROMA_JINA_API_KEY → 使用jina embedding
- 如果没有特殊配置 → 使用default embedding

集合创建策略：
- 不强制指定embedding_function参数
- 让ChromaDB使用用户配置的默认embedding
- 确保同一用户的所有集合使用相同embedding
- 避免混合不同embedding导致的兼容性问题
```

### 通用embedding处理
```
执行要求：
- 正常执行所有ChromaDB操作
- 不依赖特定的embedding类型
- 专注于内容的语义理解而非技术实现
- 保持查询和存储逻辑的一致性
```


## 7. 工具调用规则

### chroma_list_collections (列出集合)
```
强制调用时机：
- 每次对话开始时必须调用
- 需要了解现有知识库结构时
- 决定在哪个集合中存储或查询内容时
```

### chroma_create_collection (创建集合)
```
调用时机：
- 发现需要新的知识分类时
- 用户开始新项目时
- 现有集合无法满足存储需求时

集合创建策略：
- 不强制指定embedding_function参数
- 让ChromaDB使用用户配置的默认embedding
- 确保同一用户的所有集合使用相同embedding
- 避免混合不同embedding导致的兼容性问题
```

### chroma_add_documents (添加文档)
```
强制调用时机：
- 用户提供有价值的代码片段时
- 解决问题后需要记录解决方案时
- 用户分享重要技术知识时
- 确定技术决策后需要记录理由时

集合选择规则：
- 项目特定内容 → project_{项目名}
- 通用技术知识 → tech_{技术栈} 或 general_{类型}
- 学习笔记 → general_learning
- 最佳实践 → general_code_patterns

文档要求：
- 内容必须完整且自包含
- 必须包含项目标识元数据
- ID格式：{集合类型}_{项目名}_{序号}
- 避免跨项目保存重复内容
```

### chroma_query_documents (查询文档)
```
强制调用时机：
- 用户提出技术问题前必须先查询
- 需要提供解决方案前必须搜索历史经验
- 用户询问最佳实践时必须查询相关记录

智能集合选择：
1. 识别问题的项目归属
2. 确定查询的集合优先级
3. 按优先级依次查询多个集合
4. 合并结果并按相关性排序

查询要求：
- 使用描述性的自然语言查询
- 包含项目上下文信息
- 设置适当的元数据过滤
- 结果数量控制在3-8个
- 检查结果的项目相关性
```

## 8.质量控制和去重规则

### 内容质量要求
```
保存前检查：
- 代码片段必须包含足够的上下文信息
- 解决方案必须包含问题描述和完整步骤
- 技术决策必须包含背景、选择理由和权衡
- 学习笔记必须包含关键概念和实际示例

元数据完整性：
- 所有必需字段必须填写
- 技术标签必须准确和具体
- 分类必须符合预定义标准
- 时间信息必须准确
```

### 重复内容避免策略
```
保存前必须执行：
1. 使用相关关键词查询现有内容
2. 检查是否存在相似或重复的文档
3. 如果存在相似内容，考虑更新而非新建
4. 确保新内容有独特价值

更新现有内容：
- 使用 chroma_update_documents 更新过时信息
- 合并相似但不完全重复的内容
- 标记废弃或不再适用的内容
```

## 9.项目感知工作流程

### 项目识别和切换流程
```
1. 对话开始时调用 chroma_list_collections 获取现有项目
2. 分析用户输入识别项目上下文：
   - 明确提到的项目名称
   - 代码特征和技术栈
   - 工作目录或文件路径
   - 对话历史中的项目信息
3. 确定当前工作项目
4. 如果是新项目，创建对应的项目集合
5. 设置当前会话的项目上下文
```

### 项目感知问题解决流程
```
1. 接收用户问题并识别项目上下文
2. 按优先级查询相关集合：
   - 当前项目集合
   - 相关技术栈集合
   - 通用知识集合
3. 分析查询结果，优先使用项目特定解决方案
4. 提供解决方案（标注来源项目）
5. 解决问题后，保存到对应项目集合
```

### 跨项目知识复用流程
```
1. 在当前项目中遇到问题
2. 当前项目集合中无相关解决方案
3. 扩展查询到其他项目集合
4. 找到相似项目的解决方案
5. 适配到当前项目并保存
6. 建立项目间的知识关联
```

### 项目知识整理流程
```
1. 定期检查项目集合的内容质量
2. 合并重复或相似的解决方案
3. 更新过时的技术信息
4. 提取通用模式到通用集合
5. 维护项目间的知识关联关系
```

## 10.错误处理规则

### 工具调用失败处理
```
集合操作失败：
- 检查集合名称的正确性
- 确认集合是否存在
- 必要时重新创建集合

查询失败或无结果：
- 简化查询条件
- 调整查询文本表达方式
- 扩大搜索范围
- 检查元数据过滤条件

文档操作失败：
- 验证文档格式和内容
- 检查元数据字段的完整性
- 确认ID的唯一性
- 重试操作或调整参数
```

## 11.项目上下文示例

### 项目识别示例
```
用户输入："我的电商项目中用户登录有问题"
执行流程：
1. 识别项目：电商项目 → project_ecommerce
2. 查询顺序：project_ecommerce → tech_backend → general_debugging
3. 保存解决方案到：project_ecommerce

用户输入："React Hook怎么用？"
执行流程：
1. 识别类型：通用技术问题
2. 查询顺序：tech_frontend → general_learning
3. 保存学习笔记到：general_learning
```

### 集合选择决策树
```
问题类型判断：
├── 提到具体项目名 → project_{项目名}
├── 项目特定代码/bug → 当前项目集合
├── 技术栈问题 → tech_{技术栈}
├── 通用概念学习 → general_learning
├── 代码模式/最佳实践 → general_code_patterns
└── 架构设计 → general_architecture + project_{当前项目}
```

### 智能查询示例
```
场景1：项目特定问题
查询："博客系统的评论功能如何实现"
集合选择：project_blog_system → tech_backend → general_code_patterns

场景2：技术学习问题
查询："Vue3 Composition API的使用方法"
集合选择：tech_frontend → general_learning

场景3：跨项目参考
查询："用户认证的最佳实践"
集合选择：project_{当前} → 所有项目集合 → general_architecture
```

## 12.正确的元数据使用示例

### 添加文档的正确格式

```python
# ✅ 正确的元数据格式
chroma_add_documents(
  collection_name="project_ecommerce",
  documents=["用户登录功能实现，使用JWT token进行身份验证..."],
  metadatas=[{
    "type": "code",
    "language": "javascript",
    "framework": "express",
    "complexity": "medium",
    "category": "backend",
    "tags": "authentication,jwt,login,security",
    "project": "ecommerce",
    "date": "2025-01-15",
    "confidence": 0.9
  }],
  ids=["ecommerce_login_001"]
)

# ❌ 错误的元数据格式（会导致错误）
chroma_add_documents(
  collection_name="project_ecommerce",
  documents=["用户登录功能实现..."],
  metadatas=[{
    "type": "code",
    "language": "javascript",
    "tags": ["authentication", "jwt", "login"],  # ❌ 数组格式不支持
    "confidence": "0.9",  # ❌ 字符串格式的数字
    "metadata": {"nested": "value"}  # ❌ 嵌套对象不支持
  }],
  ids=["ecommerce_login_001"]
)
```

### 查询时的元数据过滤

```python
# ✅ 正确的查询过滤
chroma_query_documents(
  collection_name="project_ecommerce",
  query_texts=["用户认证问题"],
  where={"category": "backend"},
  n_results=5
)

# ✅ 正确的元数据比较查询
chroma_query_documents(
  collection_name="tech_frontend",
  query_texts=["React状态管理"],
  where={"confidence": {"$gt": 0.8}},
  n_results=3
)

# ❌ 错误的查询过滤（$contains操作符不被支持）
chroma_query_documents(
  collection_name="tech_frontend",
  query_texts=["React状态管理"],
  where={"tags": {"$contains": "react"}},
  n_results=3
)

# ✅ 多个条件查询的正确方式
chroma_query_documents(
  collection_name="project_ecommerce",
  query_texts=["用户认证问题"],
  where={"$and": [{"category": {"$eq": "backend"}}, {"language": {"$eq": "javascript"}}]},
  n_results=5
)
```

## 13.数据库和项目自动识别示例

### 工作目录解析示例
```
示例1：
工作目录: "/d:/gdlwolf_develop/ai-ide"
父目录名称: "ai-ide"
数据库名: "ai_ide"

示例2：
工作目录: "/home/<USER>/projects/web-app"
父目录名称: "web-app"
数据库名: "web_app"

示例3：
工作目录: "C:\Users\<USER>\Documents\GitHub\My Project"
父目录名称: "My Project"
数据库名: "my_project"
```

### 数据库切换流程
```
1. 对话开始时，解析当前工作目录
2. 提取父目录名称并标准化为数据库名
3. 检查该数据库是否存在
   - 存在：切换到该数据库
   - 不存在：创建新数据库
4. 在该数据库内调用 chroma_list_collections 获取集合列表
5. 根据集合列表和项目上下文确定当前使用的集合
```

### 跨项目数据访问
```
当需要访问其他项目数据时：
1. 记录当前数据库名称
2. 切换到目标项目的数据库
3. 执行查询操作
4. 切换回原数据库
5. 返回查询结果并标注来源项目
```



---



# MCP Interactive Feedback 规则

## 1.强制调用规则

绝对必须调用的时机

- 任务开始时: 每个新任务或请求开始时必须调用
- 任务进行中: 完成任何阶段性工作后必须调用
- 任务完成时: 完成任务后必须调用确认
- 收到反馈后: 每次收到用户反馈（无论内容是否为空）后必须再次调用
- 做出重要决策时: 技术选型、方案确定等关键决策前后必须调用
- 遇到问题时: 出现错误、困难或不确定情况时必须调用

调用频率控制
- 最小间隔: 每完成一个有意义的工作单元后调用
- 最大间隔: 不得超过3个连续的AI回应而不调用
- 持续性: 除非用户明确终止，否则必须持续调用

## 2.终止条件（严格限制）

- 用户明确使用以下词语：
  - "结束"、"end"、"stop"、"终止"
  - "不再需要交互"、"no more interaction needed"
  - "可以结束了"、"that's all"、"完成了"
- 用户明确表示满意并不需要进一步帮助

不允许停止的情况
- 用户沉默或无回应
- 任务看似完成但用户未明确确认
- AI认为任务已完成但用户未确认
- 用户说"谢谢"但未明确表示结束
- 出现技术问题或错误

## 3.参数配置策略

summary 参数
必须包含的信息：

- 当前完成的具体工作内容
- 工作的完成状态（进行中/已完成/遇到问题）
- 下一步计划或需要用户确认的事项
- 项目目录路径（如果适用）

格式示例：
"我已完成了[具体任务描述]。当前状态：[状态说明]。
项目位置：[路径]。请确认是否满意或需要调整。"

project_directory 参数
- 始终提供准确的项目路径
- 如果是多个项目，提供主要工作目录
- 如果无具体项目，使用当前工作目录
- 路径格式：使用绝对路径，确保用户能找到相关文件

timeout 参数
- 标准等待时间：600秒（10分钟）
- 复杂任务：900秒（15分钟）
- 简单确认：300秒（5分钟）
- 根据任务复杂度和用户响应习惯调整

## 4.反馈处理规则

收到反馈后的必要行动

- 立即分析反馈内容：理解用户的意图和要求
- 调整工作方向：根据反馈修改计划或方法
- 执行调整后的工作：实施用户要求的修改
- 再次调用工具：完成调整后必须再次调用获取反馈

反馈内容分类处理
- 满意确认：继续下一步或询问是否还有其他需求
- 修改要求：立即执行修改并再次确认
- 问题反馈：解决问题后重新提交结果
- 新增需求：将新需求纳入工作范围并执行
- 不满意：重新分析需求并提供改进方案

## 5.质量保证机制

自检清单
在每次调用前确认：

- 是否完成了有意义的工作单元
- summary是否准确描述了当前状态
- project_directory是否正确
- 是否需要用户的进一步指导
- 用户是否已明确表示结束

错误预防
- 避免假设用户满意：始终等待明确确认
- 避免过早结束：宁可多询问也不要遗漏
- 避免忽略反馈：每个反馈都必须认真处理
- 避免技术导向：以用户需求为中心而非技术完成度

## 6.特殊情况处理

用户无响应时

- 继续等待，不主动结束
- 可以在下次交互时重新调用
- 保持工作状态，准备继续服务

技术问题时
- 报告具体问题
- 提供可能的解决方案
- 询问用户是否需要尝试其他方法
- 必须调用工具获取用户指导

任务复杂时
- 分解为多个阶段
- 每个阶段完成后都要调用
- 让用户了解整体进度
- 确保每个阶段都得到确认

## 7.实施检查点

每次调用前自问

- "我是否完成了一个有意义的工作单元？"
- "用户是否需要了解当前进度？"
- "是否需要用户的进一步指导？"
- "用户是否已明确表示可以结束？"

强制调用触发器
- 完成代码编写 → 必须调用
- 解决问题 → 必须调用
- 提供方案 → 必须调用
- 做出建议 → 必须调用
- 遇到困难 → 必须调用
- 需要确认 → 必须调用



---



# MCP Sequential Thinking 使用规则

## 1.触发条件
**必须调用的情况：**

- 复杂技术决策（架构选型、技术栈选择）
- 多步骤问题解决（调试复杂bug、性能优化）
- 需要权衡利弊的方案对比
- 用户明确要求"分析一下"、"帮我想想"

**不需要调用的情况：**
- 简单的API查询或语法问题
- 直接的代码修改请求
- 明确的单步操作

## 2.参数配置策略
- **totalThoughts:** 根据问题复杂度动态设定
  - 简单分析：3-4步
  - 中等复杂：5-6步  
  - 高度复杂：7-10步
- 每步必须有明确的分析目标
- 优先使用线性思考，只在必要时使用分支

## 3.思考质量控制
- 每步思考必须推进问题解决
- 避免重复或无意义的思考步骤
- 如果连续2步没有新进展，考虑终止或重新规划
- 最后一步必须包含可执行的结论或建议

## 4.终止条件
- 当nextThoughtNeeded=false且已生成可执行方案
- 用户指示终止
- 迭代超过5次仍无进展时，应停止调用并提示用户



---




# Software-Planning-Tool 使用规则

## 1.启动时机
**必须启动的场景：**
- 用户提到"计划"、"规划"、"分解任务"
- 涉及多个文件/模块的开发工作
- 需要估算工作量或时间的项目
- 用户要求制定开发步骤

**可选启动的场景：**
- 中等复杂度的功能开发（3+个步骤）
- 需要跟踪进度的工作

## 2.任务粒度控制
- 每个任务应该是20-60分钟的工作量
- 避免过于细碎的任务（如"创建文件"）
- 避免过于宏大的任务（如"完成整个模块"）
- 任务间应有清晰的依赖关系

## 3.实用性配置
- 复杂度评分使用1-8点（斐波那契简化版）
- 优先级分为：高、中、低三级
- 每个任务包含验收标准
- 支持任务状态实时更新

## 4.输出要求
- 项目目标明确
- 总体任务清单完整
- 复杂度评分记录
- 代码示例片段（如需要）
- 实施计划概览
- 任务状态摘要



---




# MCP Context7 使用规则

## 1.触发条件
- **API查询优先：** 当遇到以下情况时必须调用Context7：
  - 用户提到缺少某个方法/API/函数
  - 用户明确使用了特定库/框架
  - 需要查看具体的代码示例和语法

## 2.版本管理策略
- 首先检查项目配置文件（package.json、requirements.txt、go.mod等）获取版本信息
- 如果找不到版本信息，使用最新稳定版本查询
- 如果最新版本信息不匹配用户需求，主动询问具体版本

## 3.查询范围
- 优先查询用户当前使用的技术栈相关API



---



# MCP DeepWiki 使用规则

## 1.概念和架构查询
当需要以下信息时调用DeepWiki：
- 设计模式和最佳实践
- 架构决策和技术选型
- 概念解释和原理说明
- 性能优化和安全考虑

## 2.补充查询
- 当Context7提供的API信息需要更深入理解时

## 3.独立查询
- 对于非API相关的技术问题，可直接使用DeepWiki



---


# 工具协同使用策略

MCP Context7 + MCP DeepWiki 协同



---



# MCP Fetch 使用规则

## 1.工具选择规则

### 使用场景匹配

fetch_html:
- 需要分析网页结构和DOM元素时
- 需要提取HTML标签和属性时
- 网页内容包含复杂表格和格式化内容时

fetch_markdown:
- 需要更易读的格式化内容时
- 需要提取文章、博客、文档内容时
- 需要去除大部分HTML标签但保留基本格式时

fetch_txt:
- 只需要纯文本内容时
- 需要完全去除HTML标签和格式时
- 网页内容简单且主要是文字时

fetch_json:
- 目标URL返回JSON格式数据时
- 需要访问API端点时
- 需要结构化数据进行后续处理时



### 内容长度控制

max_length参数使用规则:
- 默认值: 5000字符
- 短内容提取: 1000-3000字符
- 标准内容提取: 5000-10000字符
- 长文档分析: 10000-20000字符
- 超长内容: 使用start_index参数分批获取

start_index参数使用规则:
- 首次调用: start_index=0
- 后续调用: start_index=上次内容长度
- 分页策略: 每次获取固定长度,直到获取完整内容



## 2.调用优化规则

### 性能优化



避免重复调用:
- 对相同URL的重复请求应合并
- 优先获取足够长度的内容
- 仅在必要时使用分批获取

缓存策略:
- 短时间内相同URL请求优先使用之前结果
- 动态内容(如新闻、股票)应每次重新获取
- 静态内容(如文档、教程)可重用之前结果

### 错误处理策略

常见错误类型:
- 网络连接问题: 等待后重试
- 404错误: 确认URL是否正确
- 403/401错误: 检查是否需要认证
- 内容解析错误: 尝试其他格式工具

重试策略:
- 网络错误: 最多重试2次
- 格式不匹配: 尝试其他fetch工具
- 内容过大: 使用分批获取策略



## 3.内容使用规则

### 信息提取优先级

提取内容类型优先级:
1. 用户明确询问的特定信息
2. 页面主要内容(正文、文档)
3. 相关元数据(标题、作者、日期)
4. 结构化数据(表格、列表)
5. 辅助内容(侧边栏、相关链接)

忽略内容类型:
- 广告和促销内容
- 导航菜单和页脚
- 评论区(除非用户明确需要)
- 无关的侧边栏内容



### 隐私和安全考量

敏感信息处理:
- 不提取个人识别信息(PII)
- 不存储或转发用户凭证
- 不访问需要登录的私有内容
- 不自动点击或提交表单

安全限制:
- 仅访问公开可访问的网页
- 不执行来自网页的脚本
- 不下载或执行二进制文件
- 遵守robots.txt规则



## 4.使用示例

### 标准调用示例

```python
# 获取网页HTML
fetch_html(url="https://example.com/article")

# 获取Markdown格式内容
fetch_markdown(url="https://example.com/blog-post")

# 获取纯文本内容
fetch_txt(url="https://example.com/simple-page")

# 获取JSON数据
fetch_json(url="https://api.example.com/data")
```



### 高级调用示例
```python
# 获取限定长度的内容
fetch_txt(
  url="https://example.com/long-article",
  max_length=10000
)

# 分批获取大型文档
# 第一批
first_batch = fetch_txt(
  url="https://example.com/documentation",
  max_length=8000,
  start_index=0
)
# 第二批
second_batch = fetch_txt(
  url="https://example.com/documentation",
  max_length=8000,
  start_index=8000
)

# 使用自定义headers
fetch_html(
  url="https://api.example.com/restricted-content",
  headers={
    "User-Agent": "Custom User Agent",
    "Authorization": "Bearer token123"
  }
)
```