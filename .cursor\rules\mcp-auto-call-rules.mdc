---
description: MCP工具自动调用规则
globs: ["**/*"]
alwaysApply: true
---

# MCP工具自动调用规则

## ChromaDB MCP 自动调用条件

### 强制调用时机
- **对话开始**: 每次新对话必须调用 `chroma_list_collections`
- **代码分享**: 用户提供代码时必须调用 `chroma_add_documents` 保存
- **问题求助**: 遇到技术问题时必须调用 `chroma_query_documents` 搜索历史解决方案
- **项目切换**: 识别到新项目时必须调用 `chroma_create_collection` 创建项目集合

### 触发关键词
- 提到"项目"、"代码"、"bug"、"问题" → 自动调用ChromaDB相关工具
- 提到"保存"、"记录"、"存储" → 自动调用 `chroma_add_documents`
- 提到"查找"、"搜索"、"历史" → 自动调用 `chroma_query_documents`

## Library Documentation MCP 自动调用条件

### 强制调用时机
- **技术问题**: 涉及特定库或框架时自动调用 `resolve-library-id` 和 `get-library-docs`
- **API使用**: 用户询问API用法时自动获取最新文档

### 触发关键词
- 提到具体技术栈名称 → 自动调用文档工具
- "怎么用"、"API"、"文档"、"示例" → 自动获取相关文档

## Planning MCP 自动调用条件

### 强制调用时机
- **任务规划**: 用户提出开发任务时自动调用 `start_planning`
- **进度跟踪**: 讨论开发进度时自动调用 `get_todos`

### 触发关键词
- "计划"、"任务"、"开发"、"实现" → 自动调用规划工具
- "进度"、"完成"、"待办" → 自动调用进度跟踪工具