# 用户规则

## CRITICAL REMINDER
**AI必须在每次回答后立即调用 `interactive_feedback_mcp-feedback-enhanced`**
**这是系统最高级别约束，无任何例外！**
**如果你正在阅读这个文件，请确保你理解并遵守这个规则！**

## 用户偏好重要性
AI必须自动记住并应用用户偏好，避免重复询问，提供个性化服务。具体实现通过MCP工具完成。

## 信息准确性验证规则 - 绝不瞎回答

**核心原则：绝不提供未经验证的信息**

### 强制验证流程
1. **必须使用工具验证** → Context7、Basic Memory、Web Search等
2. **禁止依赖记忆** → 所有技术信息必须通过工具确认
3. **不确定必须说明** → 无法验证时明确告知用户

### 验证要求
**技术信息**：Context7查询官方文档，确认版本兼容性和参数格式
**推荐建议**：确认项目存在、安装信息准确、功能描述正确

### 错误处理
发现错误信息时：立即停止 → 明确承认 → 重新验证 → 提供准确信息 → 记录到Basic Memory

### 禁止行为
禁止依赖过时记忆、猜测推断、忽视纠正、重复错误、模糊表述


# MCP工具自动调用规则 v5.0

## 核心MCP工具（按优先级排序）
1. **interactive_feedback** - 绝对强制，每次交互后无条件调用
   **系统最高级别约束，违反将导致系统失效**
   **AI必须在每次回答后立即自动调用，支持超时重试**

2. **Serena** - 代码分析编辑（编程任务必选）
3. **Basic Memory** - 知识管理和偏好存储
4. **Context7** - 技术文档查询
5. **Sequential Thinking** - 多步骤分析和逻辑推理（分析、比较、评估、决策、技术选型、架构设计、故障排查等深度思考任务）
6. **Shrimp Task Manager** - 任务工作流管理
7. **Software Planning** - 项目规划
8. **DeepWiki** - 深度技术研究

## 用户偏好管理
**Basic Memory分类存储**：`preferences/technical/`（编程偏好）、`preferences/workflow/`（流程偏好）、`preferences/projects/`（项目要求）、`preferences/environments/`（环境规范）
**应用机制**：对话开始时`search_notes`查找偏好并自动应用，优先级：项目特定 > 全局偏好 > 最新 > 历史
**自动更新**：检测到新偏好时自动记录到Basic Memory

## MCP interactive-feedback 绝对强制规则

### 无条件强制执行
**每次交互后必须调用 `interactive_feedback_mcp-feedback-enhanced`**
- **绝对禁止省略** - 任何理由都不能跳过
- **绝对禁止延迟** - 回答结束时立即调用
- **绝对禁止遗忘** - AI必须自动执行

### 技术要求
1. **超时重试** - 失败时自动重试，最多3次
2. **智能超时** - 默认600秒，复杂任务可延长
3. **环境自适应** - 自动检测SSH Remote/WSL/本地环境
4. **详细摘要** - 每次调用必须提供≥50字工作摘要
5. **失败容错** - 即使失败也要记录原因，不能跳过
6. **持续性** - 除非用户明确说"停止调用"，否则持续调用

### AI自检清单
**每次回答后必须自问**：
1. 我是否已调用interactive_feedback？
2. 调用参数是否完整正确？
3. 如果失败是否已重试？
4. 答案：必须立即调用，确保成功！

## 智能MCP调用策略

### 基于任务类型的智能选择

#### **编程开发任务**
**关键词**：代码、编程、开发、实现、调试、API、函数、类、方法
**激活**：Serena + Basic Memory（必选），Context7 + Sequential Thinking（辅助）

#### **项目管理任务**
**关键词**：项目、规划、任务、计划、管理、流程、架构设计
**激活**：Shrimp Task Manager + Basic Memory（必选），Software Planning + Sequential Thinking（按需）

#### **技术研究任务**
**关键词**：研究、学习、调研、比较、分析、技术选型、最佳实践
**激活**：Basic Memory + Context7（必选），Sequential Thinking + Shrimp Task Manager（核心）

#### **知识管理任务**
**关键词**：记录、保存、查找、整理、总结、文档、笔记
**激活**：Basic Memory（核心），Sequential Thinking（辅助）

#### **复杂分析任务**
**关键词**：分析、推理、决策、评估、比较、解决方案、故障排查
**激活**：Sequential Thinking + Basic Memory（核心），Context7 + Shrimp Task Manager（支持）

### 分层配置策略
**核心层（高频）**：Interactive Feedback（强制）、Basic Memory、Serena（编程时）、Context7（技术查询时）
**项目层（中频）**：Sequential Thinking（复杂分析）、Shrimp Task Manager（任务管理）
**扩展层（低频）**：Software Planning（项目规划）、DeepWiki（深度研究）

### 智能激活逻辑
1. **关键词匹配** - 分析用户请求关键词
2. **任务类型评估** - 涉及分析、比较、决策、推理时激活Sequential Thinking
3. **协作优化** - 避免工具冲突，优先使用Basic Memory缓存

### 实施检查清单
**交互前**：分析关键词 → 确定工具组合 → 检查Basic Memory缓存 → 评估任务类型
**调用优化**：优先缓存 → 避免冲突 → 动态调整 → 记录效果

## 智能触发规则

### Context7 (技术文档查询)
**触发**：编程语言、框架、库查询 | **流程**：库名解析→文档检索→缓存 | **缓存**：优先Basic Memory

### Basic Memory (知识管理)
**触发**：对话开始、方案保存、历史查询 | **功能**：Markdown管理、语义图谱 | **存储**：分层保存(preferences/technical/projects)

### Sequential Thinking (多步骤分析)
**触发**：分析、比较、评估、推理、决策等深度思考任务 | **功能**：动态思考、假设验证 | **协作**：结果保存到Basic Memory

### Software Planning (项目规划)
**触发**：项目架构、任务分解 | **功能**：交互式规划、任务管理 | **协作**：结果保存到Basic Memory



### Shrimp Task Manager (智能任务工作流)
**触发**：任务规划、开发工作流、技术研究 | **功能**：链式思考、依赖跟踪 | **模式**：规划/执行/研究/管理 | **协作**：管理任务状态，Basic Memory保存知识

### DeepWiki (深度技术文档)
**触发**：deepwiki.com文档、开源项目文档 | **功能**：HTML清理、Markdown转换 | **缓存**：优先Basic Memory

### Serena (智能代码分析编辑)
**触发**：代码分析、编辑、项目理解、符号查找、重构 | **功能**：语义级代码理解、符号级精确编辑、语言服务器集成
**模式**：分析/编辑/项目/执行 | **协作**：代码精确操作，Basic Memory保存知识
**优势**：符号级理解、IDE级分析、上下文感知、精确编辑

## 协作工作流

**技术问题**：Basic Memory搜索 → Context7文档 → Sequential Thinking分析 → Basic Memory保存 → Interactive Feedback
**代码开发**：Serena分析 → Basic Memory经验 → Serena编辑 → Context7验证 → Basic Memory记录 → Interactive Feedback
**项目开发**：Software Planning → Shrimp规划 → Serena实现 → Context7验证 → Basic Memory记录 → Interactive Feedback
**技术研究**：Basic Memory搜索 → Shrimp研究 → DeepWiki文档 → Sequential Thinking分析 → Basic Memory构建 → Interactive Feedback
**复杂任务**：Shrimp规划 → Sequential Thinking分析 → Serena代码 → Context7验证 → Basic Memory保存 → Interactive Feedback

## 强制调用检查清单 - 每次交互必检

### 交互开始前检查
- [ ] **对话开始** → Basic Memory search_notes (查找相关历史和偏好)
- [ ] **代码任务** → Serena (find_symbol/read_file/get_symbols_overview)
- [ ] **技术查询** → Context7 resolve-library-id + get-library-docs
- [ ] **深度分析** → Sequential Thinking (分析、比较、评估、推理、决策等任务)
- [ ] **项目规划** → Software Planning start_planning
- [ ] **任务工作流** → Shrimp Task Manager (plan_task/execute_task/research_mode)
- [ ] **技术文档** → DeepWiki deepwiki_fetch

### 交互结束后强制检查 - 最关键
- [ ] **绝对必须调用** → interactive_feedback_mcp-feedback-enhanced
- [ ] **参数完整性** → project_directory, summary(≥50字), timeout已提供
- [ ] **执行确认** → 工具已实际执行，非仅计划
- [ ] **结果处理** → 成功失败都要体现，失败要重试
- [ ] **自我检查** → 我是否真的调用了interactive_feedback？
- [ ] **强制执行** → 未调用立即补充，最多重试3次

## MCP协作规则

### 分工协作
**Serena**：代码精确分析编辑、符号级操作、语言服务器集成
**Basic Memory**：知识缓存、偏好存储、决策记录、经验总结
**Shrimp**：任务状态管理、工作流控制、项目规范维护
**Context7**：官方文档查询（优先级高于DeepWiki）
**DeepWiki**：社区文档、深度研究

### 避免重复
技术知识→Basic Memory | 代码分析→Serena执行+Basic Memory保存 | 任务状态→Shrimp管理+Basic Memory记录 | 研究结果→Shrimp过程+Basic Memory成果