下面是我整理的我们运维的工作内容：
一、日常、周期性要做的工作
1. 日常巡检
	日常巡检，通常是做nginx日志分析，nginx日志分析我们运维自己写的分析工具，每天、每周、每月都会生成excel的分析报告，统计常见pv，uv等等。
	日常巡检还包括，查阅监控指标，每日，每周，每月的趋势，包括但不限于阿里云ecs，rds，redis，mongodb、Elasticsearch、rocketmq的性能指标。
	还包括我们自己搭建的zabbix监控
2. 日常的协助开发的工作
	例如：对数据库的crud，但是这个没啥运维技术含量，都是人家开发给我们sql，我们再执行，并导出结果。要不这个就别提了。
	导出日志
	部署项目
	配合开发调整正式服、或者测试服的配置
3. 作为桌面运维的工作
	解决同时电脑硬件和软件故障。
4. 项目周报：期刊、交通数据共享、期刊中心官网、外语平台
5. 项目月报：文泉题库
6. 发现报警，分析问题，反馈开发解决产品问题。如果是运维负责的服务器问题则运维自己解决（这半年没有发生）


二、上半年发生的非日常、非周期性的工作
1. 由于公司领导的变动，要求公司本地机房的服务器关停，全部迁移到阿里云上。这个工作完成了，比较耗时，消耗经历。
	其中涉及还在开发的项目的测试环境迁移到阿里云测试服务。
	还有涉及运维需要的服务：zabbix、uptime-kuma监控、acme.sh自动申请ssl证书，开发用的开源项目yapi，运维自己开发的分析阿里云sls日志的工具。
	
2. 	由于公司领导的变动，新领导要求我们运维做了机房设备统计，按照新规为IT设备粘贴新的“资产标签”
3. 根据要求导出项目外语平台、期刊统计数据。
4. 还安排我们接手IT设备的采购，这个事虽然安排我们做了，但是很少发生。
5. 由于阿里云响应国家政策要求，运维对阿里云短信签名进行报备。虽然这个工作挺耗时费心，但是最后好像领导又不关心了，搞的我们感觉又没啥价值了。
6. 领导安排我们调研能满足用户要求的开源产品，支持webdav、http、sftp、ftp多协议方案，最后调研了sftpgo，虽然已经让用户“试用”了，也挺领导说用户挺满意的，但是现在还没有进入正式阶段。也就是说好像还没有赚钱盈利。
7.兆泰源阿里云测试账号上的备案，迁移至兆泰源阿里云生产账号下。这个工作并不复杂，也就是简单点点阿里云按钮就行了，但是实在不知道该说点啥工作内容了，你看看这个算不算，不算就不要提了。
8. 安阳康养商城本地测试部署到安阳康养综合平台所在服务器。 这个工作是按照领导要求做的，运维其实觉得难度不大，领导也觉得不大，哎，领导都pua我们，让我们自己都觉得自己快没有价值了。
9. 配合宋斌把科创大脑交接部署给甲方，宋斌还未安排具体如何做。这个工作领导已经安排了，但是叫宋斌的领导还未安排该工作，也就是说这个工作还未进行。
10. 兆泰源阿里云服务器降配，并只保活投审稿运行中。这个任务自我感觉技术含量也不高，但是好歹也是工作。
11.将期刊邮件发送服务迁移至智能制造所在服务器。这个任务自我感觉技术含量也不高，但是好歹也是工作。
12.远程排查解决河南经贸学院项目问题。这个任务也是个历史遗留问题，现在这个项目都不维护了，领导也不在乎，虽然做了这个工作，感觉领导也不太在意。
13.文泉云盘阿里云测试服前端（read、web、admin）、期刊测试服前端（platform-admin端、jouranl-admin端、mini-program端、knowledge端）、文件中心测试服前端部署云效流水线; 这个任务自我感觉技术含量也不高，但是好歹也是工作。
14. 优化了运维nginx日志分析工具，使其可以适应同台服务器有cdn和无cdn的场景的分析。
15. ipv6调研：  1. 阿里云如何开通ipv6，ecs配置ipv6 2  优化原先应用于外语平台根据国外ip禁止访问的lua脚本，使其支持ipv6的检测（准确度以来ipv6库的准确度）。
16. 浩辰云图docker部署；这个算是日常工作协助开发部署一些环境。感觉技术含量也不高，但是好歹也是工作。

三、上半年的问题
上班年其实运维也经常性的及时的发现问题，分析问题，反馈问题。比如某些项目的ECS服务器cpu高了，带宽100%，数据库RDS CPU100%了等等。但是比较典型的，运维在其中我觉得作用比较大的案例如下：
1. 比如经常性的java 占用cpu高，运维可以及时的通过show-busy-java-threads.sh脚本定位异常cpu占用的线程，帮助开发定位具体的代码。
2. 期刊项目的MQ队列挤压，运维听取开发描述的原因是因为项目调用阿里云ip地理查询接口慢导致的，运维建议开发调用的ip查询可以缓存，这样可以快，还可以省钱。
3. 期刊项目xml解析 导致java cpu占用高，一开始开发认为就是递归函数导致的，没有继续深度优化。运维提醒开发：递归函数本身并不是高CPU的代名词，只有业务处理代码部分才是占用cpu高的主要原因，请从业务处理部分的代码优化入手，而不是递归函数。
最后开发：优化了string的replace逻辑，替换为apache commons io的StringUtils的replace，问题解决了。
4. 外语平台项目磁盘爆满。这个问题是运维发现是因为生产服务器上无故产生了大量的“heapdump数字.hprof”文件导致的，运维排查并非是java配置项导致的，开发也没有找到原因。后来运维通过搜索查到项目是开通了/actuator/heapdump接口导致的，运维通过nginx禁用了外网访问这个接口，解决了这个问题。
5. 期刊项目磁盘爆满。这个问题发生时服务器上通过df -h查看的确磁盘满了，但是ncdu分析时又看不出来哪个路径下的文件大。后来另一个运维同事通过lsof |grep deleted定位到时因为journal-admin java 没有close掉哪些删除的文件的句柄，导致了文件好像删除了，但是依然没有释放实现真正的删除。导致磁盘爆满。