{"mcpProxy": {"baseURL": "http://127.0.0.1", "addr": ":10000", "name": "tbxark-mcp-proxy", "version": "1.0.0", "type": "streamable-http"}, "mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DESKTOP_MODE": "false", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}, "autoApprove": ["interactive_feedback"]}, "basic-memory": {"command": "uvx", "args": ["basic-memory", "mcp"]}, "software-planning-tool": {"command": "node", "args": ["D:/software/14-ai/mcp/Software-planning-mcp/build/index.js"], "disabled": false, "autoApprove": []}, "Sequential-Thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "D:/software/14-ai/mcp/mcp-shrimp-task-manager/data_dir", "TEMPLATES_USE": "zh", "ENABLE_GUI": "true"}}, "serena": {"command": "uv", "args": ["run", "--directory", "D:/software/14-ai/mcp/serena", "serena-mcp-server"]}, "excel-stdio": {"command": "uvx", "args": ["excel-mcp-server", "stdio"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "time": {"command": "uvx", "args": ["mcp-server-time"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}}}